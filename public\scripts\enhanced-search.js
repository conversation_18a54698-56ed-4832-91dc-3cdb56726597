/**
 * Enhanced Search System for Cheers Marketplace
 * Provides fuzzy search, suggestions, and advanced filtering
 */

class EnhancedSearch {
  constructor() {
    this.products = [];
    this.filteredProducts = [];
    this.currentQuery = '';
    this.currentFilters = {
      category: '',
      condition: '',
      priceMin: null,
      priceMax: null,
      sortBy: 'relevance'
    };
    this.searchEventId = null;
    this.suggestionIndex = -1;
    this.suggestions = [];
    
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadProducts();
    this.populateCategories();
    this.applyUrlParameters();
  }

  bindEvents() {
    const searchInput = document.getElementById('enhanced-search-input');
    const clearButton = document.getElementById('clear-search');
    const suggestionsContainer = document.getElementById('search-suggestions');
    
    if (searchInput) {
      // Search input events
      searchInput.addEventListener('input', this.debounce((e) => {
        this.handleSearchInput(e.target.value);
      }, 300));
      
      searchInput.addEventListener('keydown', (e) => {
        this.handleKeyNavigation(e);
      });
      
      searchInput.addEventListener('focus', () => {
        this.showSuggestions();
      });
      
      searchInput.addEventListener('blur', () => {
        // Delay hiding to allow for suggestion clicks
        setTimeout(() => this.hideSuggestions(), 150);
      });
    }

    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.clearSearch();
      });
    }

    // Filter events
    const filterElements = [
      'category-filter',
      'condition-filter', 
      'price-min',
      'price-max',
      'sort-filter'
    ];

    filterElements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => {
          this.updateFilters();
        });
      }
    });

    // Filter action buttons
    const applyButton = document.getElementById('apply-filters');
    const clearButton2 = document.getElementById('clear-filters');
    
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        this.applyFilters();
      });
    }
    
    if (clearButton2) {
      clearButton2.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Click outside to close suggestions
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.enhanced-search-container')) {
        this.hideSuggestions();
      }
    });
  }

  async loadProducts() {
    try {
      // Try to get products from global variable first
      if (window.productsData) {
        this.products = window.productsData;
      } else {
        // Fallback to API call
        const response = await fetch('/api/products.json');
        this.products = await response.json();
      }
      
      this.filteredProducts = [...this.products];
      this.updateResultsCount();
    } catch (error) {
      console.error('Failed to load products:', error);
    }
  }

  populateCategories() {
    const categorySelect = document.getElementById('category-filter');
    if (!categorySelect) return;

    const categories = [...new Set(this.products.map(p => p.category))].sort();
    
    // Clear existing options except the first one
    while (categorySelect.children.length > 1) {
      categorySelect.removeChild(categorySelect.lastChild);
    }
    
    categories.forEach(category => {
      const option = document.createElement('option');
      option.value = category;
      option.textContent = category;
      categorySelect.appendChild(option);
    });
  }

  handleSearchInput(query) {
    this.currentQuery = query.trim();
    
    // Show/hide clear button
    const clearButton = document.getElementById('clear-search');
    if (clearButton) {
      clearButton.style.display = this.currentQuery ? 'block' : 'none';
    }

    // Update suggestions
    this.updateSuggestions();
    
    // Perform search
    this.performSearch();
  }

  updateSuggestions() {
    if (!this.currentQuery || this.currentQuery.length < 2) {
      this.hideSuggestions();
      return;
    }

    this.suggestions = this.generateSuggestions(this.currentQuery);
    this.renderSuggestions();
    
    if (this.suggestions.length > 0) {
      this.showSuggestions();
    } else {
      this.hideSuggestions();
    }
  }

  generateSuggestions(query) {
    const suggestions = new Set();
    const queryLower = query.toLowerCase();
    
    // Get suggestions from product names and categories
    this.products.forEach(product => {
      // Product name suggestions
      if (product.name.toLowerCase().includes(queryLower)) {
        suggestions.add(product.name);
      }
      
      // Category suggestions
      if (product.category.toLowerCase().includes(queryLower)) {
        suggestions.add(product.category);
      }
      
      // Word-based suggestions
      const words = product.name.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.startsWith(queryLower) && word.length > queryLower.length) {
          suggestions.add(word);
        }
      });
    });

    return Array.from(suggestions).slice(0, 8);
  }

  renderSuggestions() {
    const container = document.querySelector('#search-suggestions .suggestions-content');
    if (!container) return;

    container.innerHTML = '';
    
    this.suggestions.forEach((suggestion, index) => {
      const item = document.createElement('div');
      item.className = 'suggestion-item';
      item.textContent = suggestion;
      item.setAttribute('data-index', index);
      
      item.addEventListener('click', () => {
        this.selectSuggestion(suggestion);
      });
      
      container.appendChild(item);
    });
  }

  selectSuggestion(suggestion) {
    const searchInput = document.getElementById('enhanced-search-input');
    if (searchInput) {
      searchInput.value = suggestion;
      this.currentQuery = suggestion;
      this.hideSuggestions();
      this.performSearch();
    }
  }

  showSuggestions() {
    const dropdown = document.getElementById('search-suggestions');
    if (dropdown && this.suggestions.length > 0) {
      dropdown.classList.add('show');
      
      const searchInput = document.getElementById('enhanced-search-input');
      if (searchInput) {
        searchInput.setAttribute('aria-expanded', 'true');
      }
    }
  }

  hideSuggestions() {
    const dropdown = document.getElementById('search-suggestions');
    if (dropdown) {
      dropdown.classList.remove('show');
      
      const searchInput = document.getElementById('enhanced-search-input');
      if (searchInput) {
        searchInput.setAttribute('aria-expanded', 'false');
      }
    }
    this.suggestionIndex = -1;
  }

  handleKeyNavigation(e) {
    if (!this.suggestions.length) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        this.suggestionIndex = Math.min(this.suggestionIndex + 1, this.suggestions.length - 1);
        this.highlightSuggestion();
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        this.suggestionIndex = Math.max(this.suggestionIndex - 1, -1);
        this.highlightSuggestion();
        break;
        
      case 'Enter':
        e.preventDefault();
        if (this.suggestionIndex >= 0) {
          this.selectSuggestion(this.suggestions[this.suggestionIndex]);
        } else {
          this.hideSuggestions();
          this.performSearch();
        }
        break;
        
      case 'Escape':
        this.hideSuggestions();
        break;
    }
  }

  highlightSuggestion() {
    const items = document.querySelectorAll('.suggestion-item');
    items.forEach((item, index) => {
      item.classList.toggle('highlighted', index === this.suggestionIndex);
    });
  }

  performSearch() {
    // Track search analytics
    if (this.currentQuery && typeof window.trackSearch === 'function') {
      this.searchEventId = window.trackSearch(this.currentQuery, 0, this.currentFilters);
    }

    this.applyFilters();
  }

  updateFilters() {
    this.currentFilters = {
      category: document.getElementById('category-filter')?.value || '',
      condition: document.getElementById('condition-filter')?.value || '',
      priceMin: parseFloat(document.getElementById('price-min')?.value) || null,
      priceMax: parseFloat(document.getElementById('price-max')?.value) || null,
      sortBy: document.getElementById('sort-filter')?.value || 'relevance'
    };
  }

  applyFilters() {
    this.updateFilters();
    
    let filtered = [...this.products];

    // Apply search query
    if (this.currentQuery) {
      filtered = this.fuzzySearch(filtered, this.currentQuery);
    }

    // Apply category filter
    if (this.currentFilters.category) {
      filtered = filtered.filter(p => p.category === this.currentFilters.category);
    }

    // Apply condition filter
    if (this.currentFilters.condition) {
      filtered = filtered.filter(p => p.condition === this.currentFilters.condition);
    }

    // Apply price range filter
    if (this.currentFilters.priceMin !== null) {
      filtered = filtered.filter(p => p.price >= this.currentFilters.priceMin);
    }
    if (this.currentFilters.priceMax !== null) {
      filtered = filtered.filter(p => p.price <= this.currentFilters.priceMax);
    }

    // Apply sorting
    filtered = this.sortProducts(filtered);

    this.filteredProducts = filtered;
    this.updateResultsCount();
    this.updateActiveFilters();
    this.updateProductDisplay();
    this.updateUrlParameters();

    // Update search analytics
    if (this.searchEventId && typeof window.trackSearch === 'function') {
      window.trackSearch(this.currentQuery, filtered.length, this.currentFilters);
    }
  }

  fuzzySearch(products, query) {
    const queryLower = query.toLowerCase();
    
    return products
      .map(product => {
        let score = 0;
        
        // Exact matches get highest score
        if (product.name.toLowerCase().includes(queryLower)) {
          score += 10;
        }
        if (product.description.toLowerCase().includes(queryLower)) {
          score += 5;
        }
        if (product.category.toLowerCase().includes(queryLower)) {
          score += 3;
        }
        
        // Word boundary matches
        const nameWords = product.name.toLowerCase().split(/\s+/);
        const queryWords = queryLower.split(/\s+/);
        
        queryWords.forEach(qWord => {
          nameWords.forEach(nWord => {
            if (nWord.startsWith(qWord)) {
              score += 2;
            }
          });
        });
        
        return { product, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .map(item => item.product);
  }

  sortProducts(products) {
    const sorted = [...products];
    
    switch (this.currentFilters.sortBy) {
      case 'price-low':
        return sorted.sort((a, b) => a.price - b.price);
      case 'price-high':
        return sorted.sort((a, b) => b.price - a.price);
      case 'name':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      case 'newest':
        return sorted.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
      case 'relevance':
      default:
        return sorted; // Already sorted by relevance in fuzzySearch
    }
  }

  updateResultsCount() {
    const countElement = document.getElementById('results-count');
    if (countElement) {
      const count = this.filteredProducts.length;
      countElement.textContent = `${count} product${count !== 1 ? 's' : ''} found`;
    }
  }

  updateActiveFilters() {
    const container = document.getElementById('active-filters');
    if (!container) return;

    container.innerHTML = '';

    // Add filter tags
    const filters = [];
    
    if (this.currentQuery) {
      filters.push({ type: 'search', label: `"${this.currentQuery}"`, value: this.currentQuery });
    }
    
    if (this.currentFilters.category) {
      filters.push({ type: 'category', label: this.currentFilters.category, value: this.currentFilters.category });
    }
    
    if (this.currentFilters.condition) {
      filters.push({ type: 'condition', label: this.currentFilters.condition, value: this.currentFilters.condition });
    }
    
    if (this.currentFilters.priceMin !== null || this.currentFilters.priceMax !== null) {
      const min = this.currentFilters.priceMin || 0;
      const max = this.currentFilters.priceMax || '∞';
      filters.push({ type: 'price', label: `$${min} - $${max}`, value: 'price' });
    }

    filters.forEach(filter => {
      const tag = document.createElement('div');
      tag.className = 'filter-tag';
      tag.innerHTML = `
        ${filter.label}
        <button type="button" onclick="enhancedSearch.removeFilter('${filter.type}', '${filter.value}')">×</button>
      `;
      container.appendChild(tag);
    });
  }

  removeFilter(type, value) {
    switch (type) {
      case 'search':
        this.clearSearch();
        break;
      case 'category':
        document.getElementById('category-filter').value = '';
        break;
      case 'condition':
        document.getElementById('condition-filter').value = '';
        break;
      case 'price':
        document.getElementById('price-min').value = '';
        document.getElementById('price-max').value = '';
        break;
    }
    
    this.applyFilters();
  }

  clearSearch() {
    const searchInput = document.getElementById('enhanced-search-input');
    const clearButton = document.getElementById('clear-search');
    
    if (searchInput) {
      searchInput.value = '';
      this.currentQuery = '';
    }
    
    if (clearButton) {
      clearButton.style.display = 'none';
    }
    
    this.hideSuggestions();
    this.applyFilters();
  }

  clearFilters() {
    // Clear all filter inputs
    document.getElementById('category-filter').value = '';
    document.getElementById('condition-filter').value = '';
    document.getElementById('price-min').value = '';
    document.getElementById('price-max').value = '';
    document.getElementById('sort-filter').value = 'relevance';
    
    this.clearSearch();
  }

  updateProductDisplay() {
    // Dispatch event for other components to listen to
    document.dispatchEvent(new CustomEvent('searchResultsUpdated', {
      detail: {
        products: this.filteredProducts,
        query: this.currentQuery,
        filters: this.currentFilters
      }
    }));
  }

  updateUrlParameters() {
    const url = new URL(window.location.href);
    
    // Update search parameters
    if (this.currentQuery) {
      url.searchParams.set('q', this.currentQuery);
    } else {
      url.searchParams.delete('q');
    }
    
    if (this.currentFilters.category) {
      url.searchParams.set('category', this.currentFilters.category);
    } else {
      url.searchParams.delete('category');
    }
    
    if (this.currentFilters.condition) {
      url.searchParams.set('condition', this.currentFilters.condition);
    } else {
      url.searchParams.delete('condition');
    }
    
    if (this.currentFilters.priceMin !== null) {
      url.searchParams.set('min_price', this.currentFilters.priceMin);
    } else {
      url.searchParams.delete('min_price');
    }
    
    if (this.currentFilters.priceMax !== null) {
      url.searchParams.set('max_price', this.currentFilters.priceMax);
    } else {
      url.searchParams.delete('max_price');
    }
    
    if (this.currentFilters.sortBy !== 'relevance') {
      url.searchParams.set('sort', this.currentFilters.sortBy);
    } else {
      url.searchParams.delete('sort');
    }
    
    window.history.replaceState({}, '', url.toString());
  }

  applyUrlParameters() {
    const url = new URL(window.location.href);
    
    // Apply search query
    const query = url.searchParams.get('q');
    if (query) {
      const searchInput = document.getElementById('enhanced-search-input');
      if (searchInput) {
        searchInput.value = query;
        this.currentQuery = query;
      }
    }
    
    // Apply filters
    const category = url.searchParams.get('category');
    if (category) {
      const categorySelect = document.getElementById('category-filter');
      if (categorySelect) categorySelect.value = category;
    }
    
    const condition = url.searchParams.get('condition');
    if (condition) {
      const conditionSelect = document.getElementById('condition-filter');
      if (conditionSelect) conditionSelect.value = condition;
    }
    
    const minPrice = url.searchParams.get('min_price');
    if (minPrice) {
      const minInput = document.getElementById('price-min');
      if (minInput) minInput.value = minPrice;
    }
    
    const maxPrice = url.searchParams.get('max_price');
    if (maxPrice) {
      const maxInput = document.getElementById('price-max');
      if (maxInput) maxInput.value = maxPrice;
    }
    
    const sort = url.searchParams.get('sort');
    if (sort) {
      const sortSelect = document.getElementById('sort-filter');
      if (sortSelect) sortSelect.value = sort;
    }
    
    // Apply the filters
    setTimeout(() => {
      this.applyFilters();
    }, 100);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// Initialize enhanced search when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.enhancedSearch = new EnhancedSearch();
});

// Load search analytics utilities
if (typeof window !== 'undefined') {
  // Load search analytics script
  const script = document.createElement('script');
  script.src = '/scripts/search-analytics.js';
  script.async = true;
  document.head.appendChild(script);
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedSearch;
}
