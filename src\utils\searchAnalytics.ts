/**
 * Search Analytics for Cheers Marketplace
 * Tracks search behavior and provides insights
 */

export interface SearchEvent {
  id: string;
  query: string;
  timestamp: number;
  resultsCount: number;
  clickedResult?: string; // Product ID that was clicked
  filters?: {
    category?: string;
    priceRange?: [number, number];
    condition?: string;
  };
  sessionId: string;
  userAgent?: string;
}

export interface SearchAnalytics {
  totalSearches: number;
  uniqueQueries: number;
  averageResultsPerSearch: number;
  topQueries: Array<{ query: string; count: number; avgResults: number }>;
  noResultQueries: Array<{ query: string; count: number }>;
  clickThroughRate: number;
  popularFilters: Record<string, number>;
  searchTrends: Array<{ date: string; searches: number }>;
}

class SearchAnalyticsManager {
  private events: SearchEvent[] = [];
  private readonly STORAGE_KEY = 'cheers_search_analytics';
  private readonly MAX_EVENTS = 1000; // Limit stored events
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadFromStorage();
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Track a search event
   */
  trackSearch(query: string, resultsCount: number, filters?: any): string {
    const event: SearchEvent = {
      id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim().toLowerCase(),
      timestamp: Date.now(),
      resultsCount,
      filters,
      sessionId: this.sessionId,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    };

    this.events.push(event);
    this.trimEvents();
    this.saveToStorage();

    return event.id;
  }

  /**
   * Track when a user clicks on a search result
   */
  trackResultClick(searchEventId: string, productId: string): void {
    const event = this.events.find(e => e.id === searchEventId);
    if (event) {
      event.clickedResult = productId;
      this.saveToStorage();
    }
  }

  /**
   * Get search analytics summary
   */
  getAnalytics(days: number = 30): SearchAnalytics {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    const recentEvents = this.events.filter(e => e.timestamp >= cutoffTime);

    // Calculate basic metrics
    const totalSearches = recentEvents.length;
    const uniqueQueries = new Set(recentEvents.map(e => e.query)).size;
    const averageResultsPerSearch = totalSearches > 0 
      ? recentEvents.reduce((sum, e) => sum + e.resultsCount, 0) / totalSearches 
      : 0;

    // Top queries
    const queryStats = new Map<string, { count: number; totalResults: number; clicks: number }>();
    recentEvents.forEach(event => {
      const existing = queryStats.get(event.query) || { count: 0, totalResults: 0, clicks: 0 };
      existing.count++;
      existing.totalResults += event.resultsCount;
      if (event.clickedResult) existing.clicks++;
      queryStats.set(event.query, existing);
    });

    const topQueries = Array.from(queryStats.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgResults: stats.totalResults / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // No result queries
    const noResultQueries = Array.from(queryStats.entries())
      .filter(([_, stats]) => stats.totalResults === 0)
      .map(([query, stats]) => ({ query, count: stats.count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Click-through rate
    const searchesWithClicks = recentEvents.filter(e => e.clickedResult).length;
    const clickThroughRate = totalSearches > 0 ? searchesWithClicks / totalSearches : 0;

    // Popular filters
    const popularFilters: Record<string, number> = {};
    recentEvents.forEach(event => {
      if (event.filters) {
        Object.entries(event.filters).forEach(([key, value]) => {
          if (value) {
            const filterKey = `${key}:${value}`;
            popularFilters[filterKey] = (popularFilters[filterKey] || 0) + 1;
          }
        });
      }
    });

    // Search trends (daily)
    const trends = new Map<string, number>();
    recentEvents.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      trends.set(date, (trends.get(date) || 0) + 1);
    });

    const searchTrends = Array.from(trends.entries())
      .map(([date, searches]) => ({ date, searches }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalSearches,
      uniqueQueries,
      averageResultsPerSearch,
      topQueries,
      noResultQueries,
      clickThroughRate,
      popularFilters,
      searchTrends
    };
  }

  /**
   * Get search suggestions based on popular queries
   */
  getPopularSuggestions(prefix: string, limit: number = 5): string[] {
    const analytics = this.getAnalytics(7); // Last 7 days
    return analytics.topQueries
      .filter(q => q.query.startsWith(prefix.toLowerCase()))
      .map(q => q.query)
      .slice(0, limit);
  }

  /**
   * Export analytics data
   */
  exportData(): SearchEvent[] {
    return [...this.events];
  }

  /**
   * Clear all analytics data
   */
  clearData(): void {
    this.events = [];
    this.saveToStorage();
  }

  /**
   * Load events from localStorage
   */
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load search analytics from storage:', error);
      this.events = [];
    }
  }

  /**
   * Save events to localStorage
   */
  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.events));
    } catch (error) {
      console.warn('Failed to save search analytics to storage:', error);
    }
  }

  /**
   * Trim events to prevent storage bloat
   */
  private trimEvents(): void {
    if (this.events.length > this.MAX_EVENTS) {
      // Keep the most recent events
      this.events = this.events
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, this.MAX_EVENTS);
    }
  }
}

// Singleton instance
export const searchAnalytics = new SearchAnalyticsManager();

/**
 * Track search with analytics
 */
export function trackSearch(query: string, resultsCount: number, filters?: any): string {
  return searchAnalytics.trackSearch(query, resultsCount, filters);
}

/**
 * Track result click
 */
export function trackResultClick(searchEventId: string, productId: string): void {
  searchAnalytics.trackResultClick(searchEventId, productId);
}

/**
 * Get search analytics
 */
export function getSearchAnalytics(days?: number): SearchAnalytics {
  return searchAnalytics.getAnalytics(days);
}

/**
 * Get popular search suggestions
 */
export function getPopularSuggestions(prefix: string, limit?: number): string[] {
  return searchAnalytics.getPopularSuggestions(prefix, limit);
}
