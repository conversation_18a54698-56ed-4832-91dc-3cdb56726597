---
// Admin Promotions Management Component
---

<div class="admin-section" id="section-promotions">
  <div class="promotions-header">
    <h2>Promotional Engine</h2>
    <div class="promotions-actions">
      <button id="add-promotion-btn" class="btn-primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
        </svg>
        Create Promotion
      </button>
      <button id="refresh-promotions" class="btn-secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"></polyline>
          <polyline points="1,20 1,14 7,14"></polyline>
          <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"></path>
        </svg>
        Refresh
      </button>
    </div>
  </div>

  <!-- Promotion Stats -->
  <div class="promotion-stats">
    <div class="stat-card">
      <div class="stat-icon active">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value" id="active-promotions">0</div>
        <div class="stat-label">Active</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon upcoming">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value" id="upcoming-promotions">0</div>
        <div class="stat-label">Upcoming</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon expired">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M14.5,9L12,11.5L9.5,9L8,10.5L10.5,13L8,15.5L9.5,17L12,14.5L14.5,17L16,15.5L13.5,13L16,10.5L14.5,9Z"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value" id="expired-promotions">0</div>
        <div class="stat-label">Expired</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon total">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value" id="total-promotions">0</div>
        <div class="stat-label">Total</div>
      </div>
    </div>
  </div>

  <!-- Promotion Filters -->
  <div class="promotion-filters">
    <div class="filter-group">
      <label for="promotion-status-filter">Status</label>
      <select id="promotion-status-filter" class="admin-select">
        <option value="all">All Promotions</option>
        <option value="active">Active</option>
        <option value="upcoming">Upcoming</option>
        <option value="expired">Expired</option>
      </select>
    </div>

    <div class="filter-group">
      <label for="promotion-type-filter">Type</label>
      <select id="promotion-type-filter" class="admin-select">
        <option value="all">All Types</option>
        <option value="percentage">Percentage</option>
        <option value="fixed">Fixed Amount</option>
        <option value="bogo">BOGO</option>
        <option value="category">Category</option>
        <option value="seasonal">Seasonal</option>
      </select>
    </div>

    <div class="filter-group">
      <label for="promotion-search">Search</label>
      <input id="promotion-search" type="search" placeholder="Search promotions..." class="admin-input" />
    </div>
  </div>

  <!-- Promotions List -->
  <div class="promotions-list" id="promotions-list">
    <!-- Promotions will be populated by JavaScript -->
  </div>

  <!-- Promotion Form Modal -->
  <div class="promotion-modal" id="promotion-modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="promotion-form-title">Create Promotion</h3>
        <button class="modal-close" id="close-promotion-modal">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <form class="promotion-form" id="promotion-form">
        <div class="form-grid">
          <!-- Basic Information -->
          <div class="form-section">
            <h4>Basic Information</h4>
            
            <div class="form-group">
              <label for="promo-name">Promotion Name *</label>
              <input id="promo-name" type="text" required class="admin-input" placeholder="e.g., Summer Sale 2024" />
            </div>

            <div class="form-group">
              <label for="promo-description">Description</label>
              <textarea id="promo-description" class="admin-textarea" placeholder="Describe your promotion..." rows="3"></textarea>
            </div>

            <div class="form-group">
              <label for="promo-type">Promotion Type *</label>
              <select id="promo-type" required class="admin-select">
                <option value="">Select type...</option>
                <option value="percentage">Percentage Discount</option>
                <option value="fixed">Fixed Amount Off</option>
                <option value="bogo">Buy One Get One</option>
                <option value="category">Category Discount</option>
                <option value="seasonal">Seasonal Promotion</option>
              </select>
            </div>

            <div class="form-group">
              <label for="promo-value">Value *</label>
              <input id="promo-value" type="number" required class="admin-input" placeholder="e.g., 20 for 20% or $20" min="0" step="0.01" />
              <small class="form-help" id="promo-value-help">Enter percentage (0-100) or dollar amount</small>
            </div>

            <div class="form-group">
              <label for="promo-priority">Priority</label>
              <input id="promo-priority" type="number" class="admin-input" placeholder="1-10 (higher = more important)" min="1" max="10" value="5" />
            </div>
          </div>

          <!-- Date Range -->
          <div class="form-section">
            <h4>Date Range</h4>
            
            <div class="form-group">
              <label for="promo-start-date">Start Date *</label>
              <input id="promo-start-date" type="date" required class="admin-input" />
            </div>

            <div class="form-group">
              <label for="promo-end-date">End Date *</label>
              <input id="promo-end-date" type="date" required class="admin-input" />
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input id="promo-active" type="checkbox" checked />
                <span>Active</span>
              </label>
            </div>
          </div>

          <!-- Conditions -->
          <div class="form-section">
            <h4>Conditions</h4>
            
            <div class="form-group">
              <label for="promo-min-order">Minimum Order Value</label>
              <input id="promo-min-order" type="number" class="admin-input" placeholder="e.g., 50" min="0" step="0.01" />
            </div>

            <div class="form-group">
              <label for="promo-categories">Categories</label>
              <select id="promo-categories" multiple class="admin-select">
                <!-- Categories will be populated by JavaScript -->
              </select>
              <small class="form-help">Hold Ctrl/Cmd to select multiple</small>
            </div>

            <div class="form-group">
              <label for="promo-conditions">Product Conditions</label>
              <select id="promo-conditions" multiple class="admin-select">
                <option value="New">New</option>
                <option value="Excellent">Excellent</option>
                <option value="Good">Good</option>
                <option value="Fair">Fair</option>
                <option value="Poor">Poor</option>
              </select>
            </div>

            <div class="form-group">
              <label for="promo-tags">Tags</label>
              <input id="promo-tags" type="text" class="admin-input" placeholder="summer, outdoor, gift (comma separated)" />
            </div>

            <div class="form-group">
              <label for="promo-usage-limit">Usage Limit</label>
              <input id="promo-usage-limit" type="number" class="admin-input" placeholder="Leave empty for unlimited" min="1" />
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" id="cancel-promotion" class="btn-secondary">Cancel</button>
          <button type="submit" id="save-promotion" class="btn-primary">Save Promotion</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Promotion Preview -->
  <div class="promotion-preview" id="promotion-preview" style="display: none;">
    <h4>Preview</h4>
    <div class="preview-content" id="preview-content">
      <!-- Preview will be populated by JavaScript -->
    </div>
  </div>
</div>

<style>
  .admin-section#section-promotions {
    padding: 1.5rem;
  }

  .promotions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .promotions-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .promotions-actions {
    display: flex;
    gap: 0.75rem;
  }

  /* Promotion Stats */
  .promotion-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .stat-icon.active { background: #10b981; }
  .stat-icon.upcoming { background: #3b82f6; }
  .stat-icon.expired { background: #ef4444; }
  .stat-icon.total { background: #8b5cf6; }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }

  /* Filters */
  .promotion-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
  }

  .filter-group label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  /* Promotions List */
  .promotions-list {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
  }

  .promotion-item {
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
  }

  .promotion-item:hover {
    background: #f9fafb;
  }

  .promotion-item:last-child {
    border-bottom: none;
  }

  .promotion-info {
    flex: 1;
  }

  .promotion-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }

  .promotion-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .promotion-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #9ca3af;
  }

  .promotion-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .promotion-status.active {
    background: #dcfce7;
    color: #166534;
  }

  .promotion-status.upcoming {
    background: #dbeafe;
    color: #1e40af;
  }

  .promotion-status.expired {
    background: #fef2f2;
    color: #dc2626;
  }

  .promotion-actions {
    display: flex;
    gap: 0.5rem;
  }

  .btn-icon {
    background: none;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
  }

  .btn-icon:hover {
    background: #f9fafb;
    color: #374151;
  }

  /* Modal */
  .promotion-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }

  .modal-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
  }

  .modal-close:hover {
    color: #374151;
  }

  /* Form */
  .promotion-form {
    padding: 1.5rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .form-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .form-help {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .promotions-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .promotion-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .promotion-filters {
      grid-template-columns: 1fr;
    }

    .promotion-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .modal-content {
      width: 95%;
      margin: 1rem;
    }
  }
</style>
