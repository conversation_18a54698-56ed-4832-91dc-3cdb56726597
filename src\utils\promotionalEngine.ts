/**
 * Promotional Engine for Cheers Marketplace
 * Handles discounts, BOGO offers, seasonal promotions, and promotional campaigns
 */

export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  condition: string;
  tags?: string[];
  createdAt?: string;
}

export interface Promotion {
  id: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed' | 'bogo' | 'category' | 'seasonal';
  value: number; // Percentage (0-100) or fixed amount
  conditions: PromotionConditions;
  startDate: string;
  endDate: string;
  isActive: boolean;
  priority: number; // Higher number = higher priority
  usageLimit?: number;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface PromotionConditions {
  minOrderValue?: number;
  maxOrderValue?: number;
  categories?: string[];
  conditions?: string[];
  productIds?: string[];
  tags?: string[];
  customerType?: 'new' | 'returning' | 'all';
  dayOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  timeOfDay?: { start: string; end: string }; // HH:MM format
}

export interface PromotionResult {
  promotion: Promotion;
  discount: number;
  originalPrice: number;
  finalPrice: number;
  savings: number;
  message: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
  originalPrice: number;
  finalPrice?: number;
  appliedPromotions?: PromotionResult[];
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  totalDiscount: number;
  finalTotal: number;
  appliedPromotions: PromotionResult[];
}

class PromotionalEngine {
  private promotions: Promotion[] = [];
  private readonly STORAGE_KEY = 'cheers_promotions';

  constructor() {
    this.loadPromotions();
    this.initializeSeasonalPromotions();
  }

  /**
   * Load promotions from storage
   */
  private loadPromotions(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.promotions = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load promotions:', error);
      this.promotions = [];
    }
  }

  /**
   * Save promotions to storage
   */
  private savePromotions(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.promotions));
    } catch (error) {
      console.error('Failed to save promotions:', error);
    }
  }

  /**
   * Initialize seasonal promotions
   */
  private initializeSeasonalPromotions(): void {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    const seasonalPromotions: Omit<Promotion, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'New Year Sale',
        description: '20% off everything to start the year right!',
        type: 'percentage',
        value: 20,
        conditions: {},
        startDate: `${currentYear}-01-01`,
        endDate: `${currentYear}-01-07`,
        isActive: true,
        priority: 10,
        usageCount: 0
      },
      {
        name: 'Valentine\'s Day Special',
        description: '15% off clothing and accessories',
        type: 'category',
        value: 15,
        conditions: { categories: ['Clothing', 'Accessories'] },
        startDate: `${currentYear}-02-10`,
        endDate: `${currentYear}-02-14`,
        isActive: true,
        priority: 8,
        usageCount: 0
      },
      {
        name: 'Spring Cleaning Sale',
        description: 'Buy 2 get 1 free on home items',
        type: 'bogo',
        value: 1, // Get 1 free
        conditions: { categories: ['Home Decor', 'Furniture'] },
        startDate: `${currentYear}-03-15`,
        endDate: `${currentYear}-04-15`,
        isActive: true,
        priority: 7,
        usageCount: 0
      },
      {
        name: 'Summer Clearance',
        description: '30% off summer items',
        type: 'percentage',
        value: 30,
        conditions: { tags: ['summer', 'outdoor'] },
        startDate: `${currentYear}-07-01`,
        endDate: `${currentYear}-08-31`,
        isActive: true,
        priority: 9,
        usageCount: 0
      },
      {
        name: 'Back to School',
        description: '$10 off orders over $50',
        type: 'fixed',
        value: 10,
        conditions: { minOrderValue: 50, categories: ['Books', 'Electronics'] },
        startDate: `${currentYear}-08-15`,
        endDate: `${currentYear}-09-15`,
        isActive: true,
        priority: 6,
        usageCount: 0
      },
      {
        name: 'Halloween Special',
        description: '25% off costumes and decorations',
        type: 'percentage',
        value: 25,
        conditions: { tags: ['halloween', 'costume', 'decoration'] },
        startDate: `${currentYear}-10-15`,
        endDate: `${currentYear}-10-31`,
        isActive: true,
        priority: 8,
        usageCount: 0
      },
      {
        name: 'Black Friday',
        description: '40% off everything!',
        type: 'percentage',
        value: 40,
        conditions: {},
        startDate: `${currentYear}-11-24`,
        endDate: `${currentYear}-11-24`,
        isActive: true,
        priority: 15,
        usageCount: 0
      },
      {
        name: 'Holiday Sale',
        description: 'Buy 3 get 1 free on gifts',
        type: 'bogo',
        value: 1,
        conditions: { tags: ['gift', 'holiday'] },
        startDate: `${currentYear}-12-01`,
        endDate: `${currentYear}-12-25`,
        isActive: true,
        priority: 10,
        usageCount: 0
      }
    ];

    // Add seasonal promotions if they don't exist
    seasonalPromotions.forEach(promo => {
      if (!this.promotions.some(p => p.name === promo.name)) {
        this.createPromotion(promo);
      }
    });
  }

  /**
   * Create a new promotion
   */
  createPromotion(promotionData: Omit<Promotion, 'id' | 'createdAt' | 'updatedAt'>): Promotion {
    const promotion: Promotion = {
      ...promotionData,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.promotions.push(promotion);
    this.savePromotions();
    return promotion;
  }

  /**
   * Update an existing promotion
   */
  updatePromotion(id: string, updates: Partial<Promotion>): Promotion | null {
    const index = this.promotions.findIndex(p => p.id === id);
    if (index === -1) return null;

    this.promotions[index] = {
      ...this.promotions[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.savePromotions();
    return this.promotions[index];
  }

  /**
   * Delete a promotion
   */
  deletePromotion(id: string): boolean {
    const index = this.promotions.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.promotions.splice(index, 1);
    this.savePromotions();
    return true;
  }

  /**
   * Get all promotions
   */
  getAllPromotions(): Promotion[] {
    return [...this.promotions];
  }

  /**
   * Get active promotions
   */
  getActivePromotions(): Promotion[] {
    const now = new Date();
    return this.promotions.filter(promo => {
      if (!promo.isActive) return false;
      
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      return now >= startDate && now <= endDate;
    });
  }

  /**
   * Apply promotions to a single product
   */
  applyPromotionsToProduct(product: Product): PromotionResult[] {
    const activePromotions = this.getActivePromotions();
    const applicablePromotions = activePromotions.filter(promo => 
      this.isPromotionApplicableToProduct(promo, product)
    );

    // Sort by priority (highest first)
    applicablePromotions.sort((a, b) => b.priority - a.priority);

    const results: PromotionResult[] = [];
    let currentPrice = product.price;

    for (const promotion of applicablePromotions) {
      const result = this.calculatePromotionDiscount(promotion, currentPrice, product);
      if (result.discount > 0) {
        results.push(result);
        currentPrice = result.finalPrice;
        
        // For most cases, apply only the best promotion
        if (promotion.type !== 'seasonal') {
          break;
        }
      }
    }

    return results;
  }

  /**
   * Apply promotions to entire cart
   */
  applyPromotionsToCart(items: CartItem[]): Cart {
    const cartItems: CartItem[] = items.map(item => ({
      ...item,
      finalPrice: item.originalPrice,
      appliedPromotions: []
    }));

    let subtotal = 0;
    let totalDiscount = 0;
    const allAppliedPromotions: PromotionResult[] = [];

    // Apply individual product promotions
    cartItems.forEach(item => {
      const promotionResults = this.applyPromotionsToProduct(item.product);
      
      if (promotionResults.length > 0) {
        const bestPromotion = promotionResults[0]; // Highest priority
        item.finalPrice = bestPromotion.finalPrice * item.quantity;
        item.appliedPromotions = promotionResults;
        
        const itemDiscount = (item.originalPrice - bestPromotion.finalPrice) * item.quantity;
        totalDiscount += itemDiscount;
        
        allAppliedPromotions.push({
          ...bestPromotion,
          discount: itemDiscount
        });
      } else {
        item.finalPrice = item.originalPrice * item.quantity;
      }
      
      subtotal += item.originalPrice * item.quantity;
    });

    // Apply cart-level promotions (BOGO, minimum order, etc.)
    const cartLevelPromotions = this.applyCartLevelPromotions(cartItems);
    cartLevelPromotions.forEach(promo => {
      totalDiscount += promo.discount;
      allAppliedPromotions.push(promo);
    });

    const finalTotal = Math.max(0, subtotal - totalDiscount);

    return {
      items: cartItems,
      subtotal,
      totalDiscount,
      finalTotal,
      appliedPromotions: allAppliedPromotions
    };
  }

  /**
   * Check if promotion is applicable to product
   */
  private isPromotionApplicableToProduct(promotion: Promotion, product: Product): boolean {
    const conditions = promotion.conditions;

    // Check categories
    if (conditions.categories && conditions.categories.length > 0) {
      if (!conditions.categories.includes(product.category)) {
        return false;
      }
    }

    // Check conditions (product condition)
    if (conditions.conditions && conditions.conditions.length > 0) {
      if (!conditions.conditions.includes(product.condition)) {
        return false;
      }
    }

    // Check specific product IDs
    if (conditions.productIds && conditions.productIds.length > 0) {
      if (!conditions.productIds.includes(product.id)) {
        return false;
      }
    }

    // Check tags
    if (conditions.tags && conditions.tags.length > 0) {
      const productTags = product.tags || [];
      if (!conditions.tags.some(tag => productTags.includes(tag))) {
        return false;
      }
    }

    // Check time-based conditions
    if (!this.isTimeConditionMet(conditions)) {
      return false;
    }

    return true;
  }

  /**
   * Check if time-based conditions are met
   */
  private isTimeConditionMet(conditions: PromotionConditions): boolean {
    const now = new Date();

    // Check day of week
    if (conditions.dayOfWeek && conditions.dayOfWeek.length > 0) {
      if (!conditions.dayOfWeek.includes(now.getDay())) {
        return false;
      }
    }

    // Check time of day
    if (conditions.timeOfDay) {
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const [startHour, startMin] = conditions.timeOfDay.start.split(':').map(Number);
      const [endHour, endMin] = conditions.timeOfDay.end.split(':').map(Number);
      
      const startTime = startHour * 60 + startMin;
      const endTime = endHour * 60 + endMin;
      
      if (currentTime < startTime || currentTime > endTime) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate promotion discount for a product
   */
  private calculatePromotionDiscount(
    promotion: Promotion, 
    currentPrice: number, 
    product: Product
  ): PromotionResult {
    let discount = 0;
    let finalPrice = currentPrice;
    let message = '';

    switch (promotion.type) {
      case 'percentage':
        discount = (currentPrice * promotion.value) / 100;
        finalPrice = currentPrice - discount;
        message = `${promotion.value}% off`;
        break;

      case 'fixed':
        discount = Math.min(promotion.value, currentPrice);
        finalPrice = currentPrice - discount;
        message = `$${promotion.value} off`;
        break;

      case 'category':
        if (promotion.conditions.categories?.includes(product.category)) {
          discount = (currentPrice * promotion.value) / 100;
          finalPrice = currentPrice - discount;
          message = `${promotion.value}% off ${product.category}`;
        }
        break;

      default:
        break;
    }

    return {
      promotion,
      discount,
      originalPrice: currentPrice,
      finalPrice: Math.max(0, finalPrice),
      savings: discount,
      message
    };
  }

  /**
   * Apply cart-level promotions (BOGO, etc.)
   */
  private applyCartLevelPromotions(cartItems: CartItem[]): PromotionResult[] {
    const results: PromotionResult[] = [];
    const activePromotions = this.getActivePromotions();

    for (const promotion of activePromotions) {
      if (promotion.type === 'bogo') {
        const bogoResult = this.applyBogoPromotion(promotion, cartItems);
        if (bogoResult) {
          results.push(bogoResult);
        }
      }
    }

    return results;
  }

  /**
   * Apply BOGO (Buy One Get One) promotion
   */
  private applyBogoPromotion(promotion: Promotion, cartItems: CartItem[]): PromotionResult | null {
    const eligibleItems = cartItems.filter(item => 
      this.isPromotionApplicableToProduct(promotion, item.product)
    );

    if (eligibleItems.length === 0) return null;

    // Sort by price (highest first) to give away the cheapest items
    eligibleItems.sort((a, b) => b.product.price - a.product.price);

    let totalQuantity = eligibleItems.reduce((sum, item) => sum + item.quantity, 0);
    let freeItems = Math.floor(totalQuantity / (promotion.value + 1)) * promotion.value;
    
    if (freeItems === 0) return null;

    // Calculate discount by giving away the cheapest items
    let discount = 0;
    let remainingFreeItems = freeItems;

    for (const item of eligibleItems.reverse()) { // Start with cheapest
      const freeFromThisItem = Math.min(remainingFreeItems, item.quantity);
      discount += freeFromThisItem * item.product.price;
      remainingFreeItems -= freeFromThisItem;
      
      if (remainingFreeItems === 0) break;
    }

    return {
      promotion,
      discount,
      originalPrice: 0,
      finalPrice: 0,
      savings: discount,
      message: `Buy ${promotion.value + 1} get ${promotion.value} free`
    };
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `promo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get promotion statistics
   */
  getPromotionStats(): {
    total: number;
    active: number;
    expired: number;
    upcoming: number;
  } {
    const now = new Date();
    let active = 0;
    let expired = 0;
    let upcoming = 0;

    this.promotions.forEach(promo => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);

      if (now < startDate) {
        upcoming++;
      } else if (now > endDate) {
        expired++;
      } else if (promo.isActive) {
        active++;
      }
    });

    return {
      total: this.promotions.length,
      active,
      expired,
      upcoming
    };
  }

  /**
   * Clear all promotions
   */
  clearAllPromotions(): void {
    this.promotions = [];
    this.savePromotions();
  }

  /**
   * Export promotions data
   */
  exportPromotions(): Promotion[] {
    return [...this.promotions];
  }

  /**
   * Import promotions data
   */
  importPromotions(promotions: Promotion[]): void {
    this.promotions = [...promotions];
    this.savePromotions();
  }
}

// Singleton instance
export const promotionalEngine = new PromotionalEngine();

// Convenience functions
export const createPromotion = (data: Omit<Promotion, 'id' | 'createdAt' | 'updatedAt'>) => 
  promotionalEngine.createPromotion(data);

export const applyPromotionsToProduct = (product: Product) => 
  promotionalEngine.applyPromotionsToProduct(product);

export const applyPromotionsToCart = (items: CartItem[]) => 
  promotionalEngine.applyPromotionsToCart(items);

export const getActivePromotions = () => 
  promotionalEngine.getActivePromotions();

export const getAllPromotions = () =>
  promotionalEngine.getAllPromotions();

export const updatePromotion = (id: string, updates: Partial<Promotion>) =>
  promotionalEngine.updatePromotion(id, updates);

export const deletePromotion = (id: string) =>
  promotionalEngine.deletePromotion(id);

export const getPromotionStats = () =>
  promotionalEngine.getPromotionStats();
