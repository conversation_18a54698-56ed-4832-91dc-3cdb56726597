---
// Modern Admin Panel Component - Compact integrated design
import { getAllProducts } from '../lib/products.ts';
import AdminHeader from './admin/AdminHeader.astro';
import AdminSalesDashboard from './admin/AdminSalesDashboard.astro';
import AdminProductList from './admin/AdminProductList.astro';
import AdminProductForm from './admin/AdminProductForm.astro';
import AdminCategoryManager from './admin/AdminCategoryManager.astro';
import AdminPromotions from './admin/AdminPromotions.astro';

// Load products and categories at build time for initial display
const initialProducts = await getAllProducts();

// Load categories from data file
let initialCategories = [];
try {
  const categoriesModule = await import('../data/categories.json');
  initialCategories = categoriesModule.default || [];
} catch (error) {
  console.warn('Could not load categories.json, using empty array:', error);
  initialCategories = [];
}
---

<div class="admin-container">
  <!-- Admin Access Check -->
  <div id="not-admin" class="access-denied" style="display: none;">
    <div class="access-denied-content">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
      </svg>
      <h2>Access Denied</h2>
      <p>This area is restricted to administrators only.</p>
      <a href="/" class="btn-back-home">← Back to Home</a>
    </div>
  </div>

  <div id="admin-content" class="admin-content" style="display: none;">
    <!-- Compact Integrated Admin Components -->
    <AdminHeader />
    <AdminSalesDashboard />
    <AdminProductList />
    <AdminProductForm />
    <AdminCategoryManager />
    <AdminPromotions />
  </div>
</div>

<!-- Load external JavaScript -->
<script src="/scripts/admin-panel.js" is:inline></script>
<script src="/scripts/admin-sales-dashboard.js" is:inline></script>
<script src="/scripts/promotional-engine.js" is:inline></script>
<script define:vars={{ initialProducts, initialCategories }} is:inline>
  // Initialize the admin panel when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new ModernAdminPanel(initialProducts, initialCategories);
  });
</script>
