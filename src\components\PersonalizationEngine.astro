---
// Personalization Engine Component
// Integrates all personalization features into a single component
export interface Props {
  showRecentlyViewed?: boolean;
  showRecommendations?: boolean;
  showWishlist?: boolean;
  recentlyViewedLimit?: number;
  recommendationsLimit?: number;
  layout?: 'horizontal' | 'grid';
  className?: string;
}

const {
  showRecentlyViewed = true,
  showRecommendations = true,
  showWishlist = false,
  recentlyViewedLimit = 8,
  recommendationsLimit = 8,
  layout = 'horizontal',
  className = ''
} = Astro.props;

// Import required components
import RecentlyViewedProducts from './RecentlyViewedProducts.astro';
import PersonalizedRecommendations from './PersonalizedRecommendations.astro';
---

<div class={`personalization-engine ${className}`} id="personalization-engine">
  <!-- Recently Viewed Products -->
  {showRecentlyViewed && (
    <RecentlyViewedProducts
      title="Recently Viewed"
      limit={recentlyViewedLimit}
      layout={layout}
      className="personalization-section"
    />
  )}

  <!-- Personalized Recommendations -->
  {showRecommendations && (
    <PersonalizedRecommendations
      title="Recommended for You"
      subtitle="Based on your browsing history"
      limit={recommendationsLimit}
      layout={layout}
      recommendationType="browsing"
      className="personalization-section"
    />
  )}

  <!-- Similar Products (shown on product pages) -->
  {showRecommendations && (
    <PersonalizedRecommendations
      title="Similar Products"
      subtitle="You might also like these items"
      limit={recommendationsLimit}
      layout={layout}
      recommendationType="similar"
      className="personalization-section"
    />
  )}

  <!-- Trending Products -->
  {showRecommendations && (
    <PersonalizedRecommendations
      title="Trending Now"
      subtitle="Popular items this week"
      limit={recommendationsLimit}
      layout={layout}
      recommendationType="trending"
      className="personalization-section"
    />
  )}

  <!-- Wishlist Summary (if enabled) -->
  {showWishlist && (
    <section class="wishlist-summary personalization-section" id="wishlist-summary" style="display: none;">
      <div class="section-header">
        <h2 class="section-title">Your Wishlist</h2>
        <a href="/wishlist/" class="view-all-link">View All</a>
      </div>
      <div class="wishlist-preview" id="wishlist-preview">
        <!-- Wishlist items will be populated by JavaScript -->
      </div>
    </section>
  )}

  <!-- Personalization Controls -->
  <div class="personalization-controls" id="personalization-controls" style="display: none;">
    <div class="controls-header">
      <h3>Personalization Settings</h3>
      <button class="toggle-controls" id="toggle-personalization-controls" aria-label="Toggle personalization controls">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
        </svg>
      </button>
    </div>
    
    <div class="controls-content" id="controls-content" style="display: none;">
      <div class="control-group">
        <label class="control-label">
          <input type="checkbox" id="enable-recommendations" checked />
          <span>Show personalized recommendations</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="control-label">
          <input type="checkbox" id="enable-recently-viewed" checked />
          <span>Track recently viewed products</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="control-label">
          <input type="checkbox" id="enable-browsing-history" checked />
          <span>Use browsing history for recommendations</span>
        </label>
      </div>
      
      <div class="control-actions">
        <button class="clear-data-btn" id="clear-personalization-data">
          Clear All Data
        </button>
        <button class="export-data-btn" id="export-personalization-data">
          Export Data
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .personalization-engine {
    width: 100%;
  }

  .personalization-section {
    margin-bottom: 2rem;
  }

  .personalization-section:last-child {
    margin-bottom: 0;
  }

  /* Wishlist Summary */
  .wishlist-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .wishlist-summary .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .wishlist-summary .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
  }

  .view-all-link {
    color: #3182ce;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
  }

  .view-all-link:hover {
    color: #2c5aa0;
    text-decoration: underline;
  }

  .wishlist-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }

  /* Personalization Controls */
  .personalization-controls {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 300px;
  }

  .controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .controls-header h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
  }

  .toggle-controls {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
  }

  .toggle-controls:hover {
    color: #4a5568;
  }

  .controls-content {
    padding: 1rem;
  }

  .control-group {
    margin-bottom: 0.75rem;
  }

  .control-group:last-child {
    margin-bottom: 0;
  }

  .control-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: #4a5568;
  }

  .control-label input[type="checkbox"] {
    margin: 0;
  }

  .control-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .clear-data-btn,
  .export-data-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: white;
    color: #4a5568;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .clear-data-btn:hover {
    background: #fed7d7;
    border-color: #fc8181;
    color: #c53030;
  }

  .export-data-btn:hover {
    background: #bee3f8;
    border-color: #63b3ed;
    color: #3182ce;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .personalization-controls {
      bottom: 1rem;
      right: 1rem;
      left: 1rem;
      max-width: none;
    }

    .wishlist-preview {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .control-actions {
      flex-direction: column;
    }
  }

  /* Hide controls by default - shown via JavaScript when appropriate */
  .personalization-controls {
    display: none;
  }

  /* Animation for smooth transitions */
  .personalization-section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .personalization-section.loaded {
    opacity: 1;
    transform: translateY(0);
  }

  /* Loading states */
  .personalization-section.loading {
    opacity: 0.6;
    pointer-events: none;
  }

  .personalization-section.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }

  /* Privacy notice */
  .privacy-notice {
    background: #edf2f7;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 1rem;
    font-size: 0.75rem;
    color: #4a5568;
    text-align: center;
  }

  .privacy-notice a {
    color: #3182ce;
    text-decoration: none;
  }

  .privacy-notice a:hover {
    text-decoration: underline;
  }
</style>

<!-- Load required scripts -->
<script src="/scripts/customer-engagement.js" is:inline></script>

<script>
  // Initialize personalization engine
  document.addEventListener('DOMContentLoaded', () => {
    const engine = document.getElementById('personalization-engine');
    if (!engine) return;

    // Initialize components with staggered loading
    const sections = engine.querySelectorAll('.personalization-section');
    sections.forEach((section, index) => {
      setTimeout(() => {
        section.classList.add('loaded');
      }, index * 200);
    });

    // Initialize controls
    initializePersonalizationControls();

    // Show controls if user has interacted with personalization features
    if (hasPersonalizationData()) {
      showPersonalizationControls();
    }
  });

  function initializePersonalizationControls() {
    const toggleBtn = document.getElementById('toggle-personalization-controls');
    const controlsContent = document.getElementById('controls-content');
    const clearBtn = document.getElementById('clear-personalization-data');
    const exportBtn = document.getElementById('export-personalization-data');

    if (toggleBtn && controlsContent) {
      toggleBtn.addEventListener('click', () => {
        const isVisible = controlsContent.style.display !== 'none';
        controlsContent.style.display = isVisible ? 'none' : 'block';
      });
    }

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear all personalization data?')) {
          if (window.customerEngagement) {
            window.customerEngagement.clearAllData();
          }
        }
      });
    }

    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        if (window.customerEngagement) {
          const data = window.customerEngagement.exportData();
          downloadJSON(data, 'personalization-data.json');
        }
      });
    }

    // Bind preference checkboxes
    bindPreferenceControls();
  }

  function bindPreferenceControls() {
    const preferences = ['recommendations', 'recently-viewed', 'browsing-history'];
    
    preferences.forEach(pref => {
      const checkbox = document.getElementById(`enable-${pref}`);
      if (checkbox) {
        // Load saved preference
        const saved = localStorage.getItem(`pref-${pref}`);
        if (saved !== null) {
          checkbox.checked = saved === 'true';
        }

        // Save preference on change
        checkbox.addEventListener('change', () => {
          localStorage.setItem(`pref-${pref}`, checkbox.checked);
          applyPreference(pref, checkbox.checked);
        });

        // Apply current preference
        applyPreference(pref, checkbox.checked);
      }
    });
  }

  function applyPreference(preference, enabled) {
    const sections = document.querySelectorAll(`[id*="${preference}"]`);
    sections.forEach(section => {
      section.style.display = enabled ? 'block' : 'none';
    });
  }

  function hasPersonalizationData() {
    return localStorage.getItem('cheers_wishlist') ||
           localStorage.getItem('cheers_recently_viewed') ||
           localStorage.getItem('cheers_browsing_history');
  }

  function showPersonalizationControls() {
    const controls = document.getElementById('personalization-controls');
    if (controls) {
      controls.style.display = 'block';
    }
  }

  function downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
</script>
