---
// Enhanced Search Box with suggestions and analytics
export interface Props {
  placeholder?: string;
  showSuggestions?: boolean;
  showFilters?: boolean;
  initialQuery?: string;
  className?: string;
}

const {
  placeholder = "Search products...",
  showSuggestions = true,
  showFilters = true,
  initialQuery = "",
  className = ""
} = Astro.props;
---

<div class={`enhanced-search-container ${className}`}>
  <!-- Main Search Input -->
  <div class="search-input-container">
    <div class="search-input-wrapper">
      <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.35-4.35"></path>
      </svg>
      
      <input
        id="enhanced-search-input"
        type="search"
        placeholder={placeholder}
        value={initialQuery}
        class="search-input"
        autocomplete="off"
        spellcheck="false"
        aria-label="Search products"
        aria-expanded="false"
        aria-haspopup="listbox"
        role="combobox"
      />
      
      <button 
        id="clear-search" 
        class="clear-button" 
        type="button" 
        aria-label="Clear search"
        style="display: none;"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Search Suggestions Dropdown -->
    {showSuggestions && (
      <div id="search-suggestions" class="suggestions-dropdown" role="listbox" aria-label="Search suggestions">
        <div class="suggestions-content">
          <!-- Suggestions will be populated by JavaScript -->
        </div>
      </div>
    )}
  </div>

  <!-- Advanced Filters -->
  {showFilters && (
    <div class="search-filters">
      <div class="filters-row">
        <!-- Price Range Filter -->
        <div class="filter-group">
          <label for="price-min">Price Range</label>
          <div class="price-range-inputs">
            <input
              id="price-min"
              type="number"
              placeholder="Min"
              min="0"
              step="0.01"
              class="price-input"
              aria-label="Minimum price"
            />
            <span class="price-separator">to</span>
            <input
              id="price-max"
              type="number"
              placeholder="Max"
              min="0"
              step="0.01"
              class="price-input"
              aria-label="Maximum price"
            />
          </div>
        </div>

        <!-- Condition Filter -->
        <div class="filter-group">
          <label for="condition-filter">Condition</label>
          <select id="condition-filter" class="filter-select">
            <option value="">All Conditions</option>
            <option value="New">New</option>
            <option value="Excellent">Excellent</option>
            <option value="Good">Good</option>
            <option value="Fair">Fair</option>
            <option value="Poor">Poor</option>
          </select>
        </div>

        <!-- Category Filter -->
        <div class="filter-group">
          <label for="category-filter">Category</label>
          <select id="category-filter" class="filter-select">
            <option value="">All Categories</option>
            <!-- Categories will be populated by JavaScript -->
          </select>
        </div>

        <!-- Sort Options -->
        <div class="filter-group">
          <label for="sort-filter">Sort By</label>
          <select id="sort-filter" class="filter-select">
            <option value="relevance">Relevance</option>
            <option value="newest">Newest First</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="name">Name A-Z</option>
          </select>
        </div>
      </div>

      <!-- Filter Actions -->
      <div class="filter-actions">
        <button id="apply-filters" class="apply-filters-btn" type="button">
          Apply Filters
        </button>
        <button id="clear-filters" class="clear-filters-btn" type="button">
          Clear All
        </button>
      </div>
    </div>
  )}

  <!-- Search Results Summary -->
  <div class="search-results-summary">
    <span id="results-count">0 products found</span>
    <div class="active-filters" id="active-filters">
      <!-- Active filter tags will be shown here -->
    </div>
  </div>
</div>

<style>
  .enhanced-search-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }

  .search-input-container {
    position: relative;
    margin-bottom: 1rem;
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
  }

  .search-input-wrapper:focus-within {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .search-icon {
    color: #6b7280;
    margin-right: 0.75rem;
    flex-shrink: 0;
  }

  .search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
  }

  .search-input::placeholder {
    color: #9ca3af;
  }

  .clear-button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
  }

  .clear-button:hover {
    color: #374151;
  }

  .suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
  }

  .suggestions-dropdown.show {
    display: block;
  }

  .suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
  }

  .suggestion-item:hover,
  .suggestion-item.highlighted {
    background-color: #f9fafb;
  }

  .suggestion-item:last-child {
    border-bottom: none;
  }

  .search-filters {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
  }

  .filter-group label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .filter-select,
  .price-input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
  }

  .filter-select:focus,
  .price-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  }

  .price-range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .price-input {
    flex: 1;
  }

  .price-separator {
    color: #6b7280;
    font-size: 0.875rem;
  }

  .filter-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
  }

  .apply-filters-btn,
  .clear-filters-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .apply-filters-btn {
    background: #2563eb;
    color: white;
    border: none;
  }

  .apply-filters-btn:hover {
    background: #1d4ed8;
  }

  .clear-filters-btn {
    background: white;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  .clear-filters-btn:hover {
    background: #f9fafb;
    color: #374151;
  }

  .search-results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  #results-count {
    font-weight: 500;
    color: #374151;
  }

  .active-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .filter-tag {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .filter-tag button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    font-size: 0.75rem;
  }

  @media (max-width: 768px) {
    .filters-row {
      grid-template-columns: 1fr;
    }
    
    .filter-actions {
      justify-content: stretch;
    }
    
    .apply-filters-btn,
    .clear-filters-btn {
      flex: 1;
    }
  }
</style>
