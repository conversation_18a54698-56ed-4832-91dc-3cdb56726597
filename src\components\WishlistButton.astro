---
// Wishlist Button Component
export interface Props {
  productId: string;
  productName: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'icon' | 'button' | 'text';
  showNotifyOption?: boolean;
  className?: string;
}

const {
  productId,
  productName,
  size = 'medium',
  variant = 'icon',
  showNotifyOption = false,
  className = ''
} = Astro.props;

const sizeClasses = {
  small: 'wishlist-btn-small',
  medium: 'wishlist-btn-medium',
  large: 'wishlist-btn-large'
};

const variantClasses = {
  icon: 'wishlist-btn-icon',
  button: 'wishlist-btn-button',
  text: 'wishlist-btn-text'
};
---

<div class={`wishlist-container ${className}`}>
  <button
    class={`wishlist-btn ${sizeClasses[size]} ${variantClasses[variant]}`}
    data-product-id={productId}
    data-product-name={productName}
    aria-label={`Add ${productName} to wishlist`}
    title="Add to wishlist"
  >
    <!-- Heart Icon (filled when in wishlist) -->
    <svg class="heart-icon heart-outline" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </svg>
    
    <svg class="heart-icon heart-filled" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" stroke="none">
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </svg>

    {variant === 'button' && (
      <span class="wishlist-text">
        <span class="add-text">Add to Wishlist</span>
        <span class="remove-text">Remove from Wishlist</span>
      </span>
    )}

    {variant === 'text' && (
      <span class="wishlist-text-only">
        <span class="add-text">Save for Later</span>
        <span class="remove-text">Saved</span>
      </span>
    )}
  </button>

  {showNotifyOption && (
    <div class="notify-option" style="display: none;">
      <label class="notify-checkbox">
        <input type="checkbox" class="notify-input" />
        <span class="checkmark"></span>
        <span class="notify-label">Notify me of sales</span>
      </label>
    </div>
  )}

  <!-- Wishlist feedback tooltip -->
  <div class="wishlist-tooltip" role="tooltip">
    <span class="tooltip-text"></span>
  </div>
</div>

<style>
  .wishlist-container {
    position: relative;
    display: inline-block;
  }

  .wishlist-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
  }

  .wishlist-btn:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
  }

  /* Size variants */
  .wishlist-btn-small {
    padding: 0.25rem;
  }

  .wishlist-btn-small .heart-icon {
    width: 16px;
    height: 16px;
  }

  .wishlist-btn-medium {
    padding: 0.5rem;
  }

  .wishlist-btn-medium .heart-icon {
    width: 20px;
    height: 20px;
  }

  .wishlist-btn-large {
    padding: 0.75rem;
  }

  .wishlist-btn-large .heart-icon {
    width: 24px;
    height: 24px;
  }

  /* Style variants */
  .wishlist-btn-icon {
    color: #6b7280;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .wishlist-btn-icon:hover {
    color: #ef4444;
    background: rgba(255, 255, 255, 0.95);
    transform: scale(1.05);
  }

  .wishlist-btn-button {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .wishlist-btn-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #ef4444;
  }

  .wishlist-btn-text {
    background: none;
    color: #6b7280;
    padding: 0.25rem 0;
    font-size: 0.875rem;
  }

  .wishlist-btn-text:hover {
    color: #ef4444;
  }

  /* Heart icon states */
  .heart-icon {
    transition: all 0.2s ease;
  }

  .heart-filled {
    display: none;
    color: #ef4444;
  }

  .wishlist-btn.in-wishlist .heart-outline {
    display: none;
  }

  .wishlist-btn.in-wishlist .heart-filled {
    display: block;
  }

  .wishlist-btn.in-wishlist {
    color: #ef4444;
  }

  /* Text states */
  .wishlist-text .remove-text,
  .wishlist-text-only .remove-text {
    display: none;
  }

  .wishlist-btn.in-wishlist .wishlist-text .add-text,
  .wishlist-btn.in-wishlist .wishlist-text-only .add-text {
    display: none;
  }

  .wishlist-btn.in-wishlist .wishlist-text .remove-text,
  .wishlist-btn.in-wishlist .wishlist-text-only .remove-text {
    display: inline;
  }

  /* Notify option */
  .notify-option {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
    white-space: nowrap;
    margin-top: 0.25rem;
  }

  .notify-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: #374151;
  }

  .notify-input {
    margin: 0;
  }

  .checkmark {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .notify-input:checked + .checkmark {
    background: #2563eb;
    border-color: #2563eb;
  }

  .notify-input:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
  }

  /* Tooltip */
  .wishlist-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-bottom: 0.25rem;
    z-index: 20;
  }

  .wishlist-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
  }

  .wishlist-tooltip.show {
    opacity: 1;
    visibility: visible;
  }

  /* Animation effects */
  .wishlist-btn.adding {
    animation: pulse 0.3s ease;
  }

  .wishlist-btn.removing {
    animation: shake 0.3s ease;
  }

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .wishlist-btn-button {
      padding: 0.375rem 0.75rem;
      font-size: 0.8125rem;
    }

    .notify-option {
      left: auto;
      right: 0;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .wishlist-btn-icon {
      border-width: 2px;
    }

    .wishlist-btn.in-wishlist {
      background: #fef2f2;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .wishlist-btn,
    .heart-icon,
    .wishlist-tooltip {
      transition: none;
    }

    .wishlist-btn.adding,
    .wishlist-btn.removing {
      animation: none;
    }
  }
</style>

<script>
  // Wishlist button functionality will be handled by the main wishlist script
  // This ensures the component is accessible and semantic
</script>
