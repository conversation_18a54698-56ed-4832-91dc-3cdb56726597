---
// Promotional Banner Component
// Displays active promotions and deals
export interface Props {
  productId?: string;
  category?: string;
  showGlobal?: boolean;
  className?: string;
  layout?: 'banner' | 'badge' | 'inline';
}

const {
  productId,
  category,
  showGlobal = true,
  className = "",
  layout = "banner"
} = Astro.props;
---

<div class={`promotional-banner ${layout} ${className}`} id="promotional-banner" style="display: none;">
  <div class="promotions-container" id="promotions-container">
    <!-- Promotions will be populated by JavaScript -->
  </div>
</div>

<style>
  .promotional-banner {
    margin: 1rem 0;
  }

  .promotional-banner.banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 1rem;
    position: relative;
    overflow: hidden;
  }

  .promotional-banner.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    opacity: 0.3;
  }

  .promotional-banner.badge {
    display: inline-block;
    background: #ef4444;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .promotional-banner.inline {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    color: #92400e;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
  }

  .promotions-container {
    position: relative;
    z-index: 1;
  }

  .promotion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .promotion-item:last-child {
    margin-bottom: 0;
  }

  .promotion-content {
    flex: 1;
  }

  .promotion-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }

  .promotional-banner.banner .promotion-title {
    font-size: 1.125rem;
  }

  .promotional-banner.badge .promotion-title,
  .promotional-banner.inline .promotion-title {
    font-size: 0.875rem;
  }

  .promotion-description {
    opacity: 0.9;
    font-size: 0.875rem;
  }

  .promotional-banner.badge .promotion-description,
  .promotional-banner.inline .promotion-description {
    font-size: 0.75rem;
  }

  .promotion-value {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.125rem;
    white-space: nowrap;
  }

  .promotional-banner.badge .promotion-value,
  .promotional-banner.inline .promotion-value {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .promotion-icon {
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

  .promotion-timer {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
  }

  .promotional-banner.badge .promotion-timer,
  .promotional-banner.inline .promotion-timer {
    font-size: 0.625rem;
  }

  /* Multiple promotions */
  .promotion-carousel {
    position: relative;
  }

  .promotion-slides {
    overflow: hidden;
  }

  .promotion-slide {
    transition: transform 0.3s ease;
  }

  .promotion-nav {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .promotion-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .promotion-dot.active {
    background: white;
  }

  /* Animations */
  .promotional-banner {
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .promotion-pulse {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .promotional-banner.banner {
      padding: 0.75rem;
    }

    .promotion-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .promotion-value {
      align-self: flex-end;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .promotional-banner.banner {
      border: 2px solid white;
    }

    .promotional-banner.inline {
      border-width: 2px;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .promotional-banner,
    .promotion-slide,
    .promotion-dot {
      animation: none;
      transition: none;
    }
  }

  /* Print styles */
  @media print {
    .promotional-banner {
      background: none !important;
      color: black !important;
      border: 1px solid black;
    }
  }
</style>

<!-- Load promotional engine script -->
<script src="/scripts/promotional-display.js"></script>

<script define:vars={{ productId, category, showGlobal, layout }}>
  // Initialize promotional banner when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    if (typeof PromotionalDisplay !== 'undefined') {
      new PromotionalDisplay({
        containerId: 'promotional-banner',
        productId,
        category,
        showGlobal,
        layout
      });
    }
  });
</script>
