/**
 * Promotional Display JavaScript
 * Handles displaying active promotions on product pages and throughout the site
 */

class PromotionalDisplay {
  constructor(options = {}) {
    this.containerId = options.containerId || 'promotional-banner';
    this.productId = options.productId;
    this.category = options.category;
    this.showGlobal = options.showGlobal !== false;
    this.layout = options.layout || 'banner';
    this.currentSlide = 0;
    this.slideInterval = null;
    
    this.init();
  }

  init() {
    this.loadPromotions();
    this.startAutoSlide();
  }

  loadPromotions() {
    try {
      const stored = localStorage.getItem('cheers_promotions');
      const allPromotions = stored ? JSON.parse(stored) : [];
      
      const activePromotions = this.getActivePromotions(allPromotions);
      const applicablePromotions = this.filterApplicablePromotions(activePromotions);
      
      if (applicablePromotions.length > 0) {
        this.displayPromotions(applicablePromotions);
      }
    } catch (error) {
      console.error('Failed to load promotions:', error);
    }
  }

  getActivePromotions(promotions) {
    const now = new Date();
    return promotions.filter(promo => {
      if (!promo.isActive) return false;
      
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      return now >= startDate && now <= endDate;
    });
  }

  filterApplicablePromotions(promotions) {
    return promotions.filter(promo => {
      // Global promotions
      if (this.showGlobal && this.isGlobalPromotion(promo)) {
        return true;
      }

      // Category-specific promotions
      if (this.category && this.isCategoryPromotion(promo)) {
        return true;
      }

      // Product-specific promotions
      if (this.productId && this.isProductPromotion(promo)) {
        return true;
      }

      return false;
    });
  }

  isGlobalPromotion(promotion) {
    const conditions = promotion.conditions || {};
    
    // No specific conditions means it's global
    return !conditions.categories && 
           !conditions.productIds && 
           !conditions.tags;
  }

  isCategoryPromotion(promotion) {
    const conditions = promotion.conditions || {};
    return conditions.categories && 
           conditions.categories.includes(this.category);
  }

  isProductPromotion(promotion) {
    const conditions = promotion.conditions || {};
    return conditions.productIds && 
           conditions.productIds.includes(this.productId);
  }

  displayPromotions(promotions) {
    const container = document.getElementById(this.containerId);
    if (!container) return;

    const promotionsContainer = container.querySelector('#promotions-container');
    if (!promotionsContainer) return;

    // Sort by priority (highest first)
    promotions.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    if (promotions.length === 1) {
      this.displaySinglePromotion(promotionsContainer, promotions[0]);
    } else {
      this.displayMultiplePromotions(promotionsContainer, promotions);
    }

    // Show the container
    container.style.display = 'block';
  }

  displaySinglePromotion(container, promotion) {
    container.innerHTML = this.createPromotionHTML(promotion);
  }

  displayMultiplePromotions(container, promotions) {
    const carouselHTML = `
      <div class="promotion-carousel">
        <div class="promotion-slides">
          ${promotions.map((promo, index) => `
            <div class="promotion-slide ${index === 0 ? 'active' : ''}" data-slide="${index}">
              ${this.createPromotionHTML(promo)}
            </div>
          `).join('')}
        </div>
        ${promotions.length > 1 ? `
          <div class="promotion-nav">
            ${promotions.map((_, index) => `
              <div class="promotion-dot ${index === 0 ? 'active' : ''}" data-slide="${index}"></div>
            `).join('')}
          </div>
        ` : ''}
      </div>
    `;

    container.innerHTML = carouselHTML;

    // Bind navigation events
    if (promotions.length > 1) {
      this.bindCarouselEvents();
    }
  }

  createPromotionHTML(promotion) {
    const icon = this.getPromotionIcon(promotion.type);
    const value = this.formatPromotionValue(promotion);
    const timeLeft = this.getTimeLeft(promotion.endDate);

    return `
      <div class="promotion-item">
        <div class="promotion-content">
          <div class="promotion-title">
            ${icon}
            ${promotion.name}
          </div>
          <div class="promotion-description">${promotion.description}</div>
          ${timeLeft ? `<div class="promotion-timer">⏰ ${timeLeft}</div>` : ''}
        </div>
        <div class="promotion-value ${promotion.type === 'bogo' ? 'promotion-pulse' : ''}">
          ${value}
        </div>
      </div>
    `;
  }

  getPromotionIcon(type) {
    const icons = {
      percentage: '🏷️',
      fixed: '💰',
      bogo: '🎁',
      category: '📂',
      seasonal: '🎉'
    };
    
    return `<span class="promotion-icon">${icons[type] || '🏷️'}</span>`;
  }

  formatPromotionValue(promotion) {
    switch (promotion.type) {
      case 'percentage':
        return `${promotion.value}% OFF`;
      case 'fixed':
        return `$${promotion.value} OFF`;
      case 'bogo':
        return `Buy ${promotion.value + 1} Get ${promotion.value}`;
      case 'category':
        return `${promotion.value}% OFF`;
      default:
        return 'SPECIAL OFFER';
    }
  }

  getTimeLeft(endDate) {
    const now = new Date();
    const end = new Date(endDate);
    const diff = end - now;

    if (diff <= 0) return null;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} day${days !== 1 ? 's' : ''} left`;
    } else if (hours > 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''} left`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''} left`;
    } else {
      return 'Ending soon!';
    }
  }

  bindCarouselEvents() {
    const dots = document.querySelectorAll('.promotion-dot');
    dots.forEach(dot => {
      dot.addEventListener('click', (e) => {
        const slideIndex = parseInt(e.target.dataset.slide);
        this.goToSlide(slideIndex);
      });
    });
  }

  goToSlide(index) {
    const slides = document.querySelectorAll('.promotion-slide');
    const dots = document.querySelectorAll('.promotion-dot');

    // Remove active classes
    slides.forEach(slide => slide.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));

    // Add active class to current slide and dot
    if (slides[index]) {
      slides[index].classList.add('active');
    }
    if (dots[index]) {
      dots[index].classList.add('active');
    }

    this.currentSlide = index;
  }

  startAutoSlide() {
    const slides = document.querySelectorAll('.promotion-slide');
    if (slides.length <= 1) return;

    this.slideInterval = setInterval(() => {
      const nextSlide = (this.currentSlide + 1) % slides.length;
      this.goToSlide(nextSlide);
    }, 5000); // Change slide every 5 seconds
  }

  stopAutoSlide() {
    if (this.slideInterval) {
      clearInterval(this.slideInterval);
      this.slideInterval = null;
    }
  }

  destroy() {
    this.stopAutoSlide();
    const container = document.getElementById(this.containerId);
    if (container) {
      container.style.display = 'none';
    }
  }

  refresh() {
    this.stopAutoSlide();
    this.loadPromotions();
    this.startAutoSlide();
  }
}

// Global promotional display functions
window.PromotionalDisplay = PromotionalDisplay;

// Auto-initialize promotional banners on page load
document.addEventListener('DOMContentLoaded', () => {
  // Initialize any promotional banners that don't have specific initialization
  const banners = document.querySelectorAll('.promotional-banner:not([data-initialized])');
  banners.forEach(banner => {
    banner.setAttribute('data-initialized', 'true');
    new PromotionalDisplay({
      containerId: banner.id,
      showGlobal: true,
      layout: banner.classList.contains('badge') ? 'badge' : 
              banner.classList.contains('inline') ? 'inline' : 'banner'
    });
  });
});

// Refresh promotions when storage changes (for real-time updates)
window.addEventListener('storage', (e) => {
  if (e.key === 'cheers_promotions') {
    // Refresh all promotional displays
    document.querySelectorAll('.promotional-banner').forEach(banner => {
      if (banner.promotionalDisplay) {
        banner.promotionalDisplay.refresh();
      }
    });
  }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PromotionalDisplay;
}
