/**
 * Fuzzy Search Implementation for Cheers Marketplace
 * Provides intelligent search with typo tolerance and relevance scoring
 */

export interface SearchResult<T> {
  item: T;
  score: number;
  matches: SearchMatch[];
}

export interface SearchMatch {
  field: string;
  value: string;
  indices: number[][];
  score: number;
}

export interface FuzzySearchOptions {
  threshold: number; // 0.0 = perfect match, 1.0 = match anything
  distance: number; // Maximum allowed distance for matches
  includeScore: boolean;
  includeMatches: boolean;
  minMatchCharLength: number;
  shouldSort: boolean;
  keys: string[]; // Fields to search in
  weights?: Record<string, number>; // Field weights for scoring
}

const DEFAULT_OPTIONS: FuzzySearchOptions = {
  threshold: 0.4,
  distance: 100,
  includeScore: true,
  includeMatches: true,
  minMatchCharLength: 1,
  shouldSort: true,
  keys: ['name', 'description', 'category'],
  weights: {
    name: 0.6,
    description: 0.3,
    category: 0.1
  }
};

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(a: string, b: string): number {
  const matrix = Array(b.length + 1).fill(null).map(() => Array(a.length + 1).fill(null));

  for (let i = 0; i <= a.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= b.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= b.length; j++) {
    for (let i = 1; i <= a.length; i++) {
      const indicator = a[i - 1] === b[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[b.length][a.length];
}

/**
 * Find fuzzy matches in a string
 */
function findMatches(pattern: string, text: string, options: FuzzySearchOptions): SearchMatch | null {
  const patternLower = pattern.toLowerCase();
  const textLower = text.toLowerCase();
  
  if (patternLower.length === 0) return null;
  
  // Exact match gets highest score
  if (textLower.includes(patternLower)) {
    const startIndex = textLower.indexOf(patternLower);
    return {
      field: '',
      value: text,
      indices: [[startIndex, startIndex + pattern.length - 1]],
      score: 1.0
    };
  }
  
  // Fuzzy matching with Levenshtein distance
  const distance = levenshteinDistance(patternLower, textLower);
  const maxLength = Math.max(patternLower.length, textLower.length);
  const score = 1 - (distance / maxLength);
  
  if (score < (1 - options.threshold)) {
    return null;
  }
  
  return {
    field: '',
    value: text,
    indices: [[0, text.length - 1]], // Simplified for now
    score
  };
}

/**
 * Main fuzzy search class
 */
export class FuzzySearch<T> {
  private options: FuzzySearchOptions;
  private items: T[];

  constructor(items: T[], options: Partial<FuzzySearchOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.items = items;
  }

  /**
   * Search for items matching the pattern
   */
  search(pattern: string): SearchResult<T>[] {
    if (!pattern || pattern.trim().length < this.options.minMatchCharLength) {
      return this.items.map(item => ({
        item,
        score: 1,
        matches: []
      }));
    }

    const results: SearchResult<T>[] = [];
    const searchPattern = pattern.trim();

    for (const item of this.items) {
      const matches: SearchMatch[] = [];
      let totalScore = 0;
      let weightSum = 0;

      // Search in each specified field
      for (const key of this.options.keys) {
        const value = this.getNestedValue(item, key);
        if (typeof value === 'string' && value.length > 0) {
          const match = findMatches(searchPattern, value, this.options);
          if (match) {
            match.field = key;
            matches.push(match);
            
            const weight = this.options.weights?.[key] || 1;
            totalScore += match.score * weight;
            weightSum += weight;
          }
        }
      }

      // Calculate final score
      if (matches.length > 0) {
        const finalScore = weightSum > 0 ? totalScore / weightSum : 0;
        
        if (finalScore >= (1 - this.options.threshold)) {
          results.push({
            item,
            score: finalScore,
            matches: this.options.includeMatches ? matches : []
          });
        }
      }
    }

    // Sort by score if requested
    if (this.options.shouldSort) {
      results.sort((a, b) => b.score - a.score);
    }

    return results;
  }

  /**
   * Get nested object value by key path
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Update the search index with new items
   */
  updateIndex(items: T[]): void {
    this.items = items;
  }

  /**
   * Get search suggestions based on partial input
   */
  getSuggestions(pattern: string, limit: number = 5): string[] {
    if (!pattern || pattern.length < 2) return [];

    const suggestions = new Set<string>();
    const searchPattern = pattern.toLowerCase();

    for (const item of this.items) {
      for (const key of this.options.keys) {
        const value = this.getNestedValue(item, key);
        if (typeof value === 'string') {
          const words = value.toLowerCase().split(/\s+/);
          
          for (const word of words) {
            if (word.startsWith(searchPattern) && word.length > searchPattern.length) {
              suggestions.add(word);
              if (suggestions.size >= limit) break;
            }
          }
          
          if (suggestions.size >= limit) break;
        }
      }
      if (suggestions.size >= limit) break;
    }

    return Array.from(suggestions).slice(0, limit);
  }
}

/**
 * Create a fuzzy search instance for products
 */
export function createProductSearch(products: any[]): FuzzySearch<any> {
  return new FuzzySearch(products, {
    threshold: 0.3,
    keys: ['name', 'description', 'category'],
    weights: {
      name: 0.6,
      description: 0.3,
      category: 0.1
    }
  });
}
