/**
 * Admin Sales Dashboard JavaScript
 * Handles analytics, charts, and sales data visualization
 */

class AdminSalesDashboard {
  constructor() {
    this.currentPeriod = 30; // days
    this.salesData = [];
    this.searchAnalytics = null;
    this.customerData = [];
    this.chart = null;
    
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadData();
    this.initializeChart();
  }

  bindEvents() {
    // Period selector
    const periodSelect = document.getElementById('dashboard-period');
    if (periodSelect) {
      periodSelect.addEventListener('change', (e) => {
        this.currentPeriod = parseInt(e.target.value);
        this.loadData();
      });
    }

    // Refresh button
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.refreshDashboard();
      });
    }

    // Chart toggles
    document.querySelectorAll('.chart-toggle').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const chartType = e.target.dataset.chart;
        this.switchChart(chartType);
      });
    });

    // Export buttons
    document.getElementById('export-sales-csv')?.addEventListener('click', () => {
      this.exportSalesData('csv');
    });

    document.getElementById('export-analytics-json')?.addEventListener('click', () => {
      this.exportAnalyticsData('json');
    });

    document.getElementById('generate-report')?.addEventListener('click', () => {
      this.generateReport();
    });
  }

  async loadData() {
    try {
      // Load sales data from Snipcart or analytics
      await this.loadSalesData();
      
      // Load search analytics
      this.loadSearchAnalytics();
      
      // Load customer engagement data
      this.loadCustomerData();
      
      // Update all dashboard components
      this.updateMetrics();
      this.updateCharts();
      this.updateAnalytics();
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      this.showError('Failed to load dashboard data');
    }
  }

  async loadSalesData() {
    // In a real implementation, this would fetch from Snipcart API
    // For now, we'll simulate sales data
    this.salesData = this.generateMockSalesData();
  }

  generateMockSalesData() {
    const data = [];
    const now = new Date();
    
    for (let i = this.currentPeriod; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // Generate realistic sales data
      const baseRevenue = 50 + Math.random() * 200;
      const orders = Math.floor(1 + Math.random() * 8);
      const revenue = orders * (baseRevenue + Math.random() * 50);
      
      data.push({
        date: date.toISOString().split('T')[0],
        revenue: Math.round(revenue * 100) / 100,
        orders: orders,
        visitors: Math.floor(20 + Math.random() * 100),
        conversions: orders
      });
    }
    
    return data;
  }

  loadSearchAnalytics() {
    // Load search analytics from the search analytics system
    if (typeof getSearchAnalytics === 'function') {
      this.searchAnalytics = getSearchAnalytics(this.currentPeriod);
    } else {
      this.searchAnalytics = {
        totalSearches: 0,
        uniqueQueries: 0,
        topQueries: [],
        clickThroughRate: 0
      };
    }
  }

  loadCustomerData() {
    // Load customer engagement data
    if (typeof window.customerEngagement !== 'undefined') {
      const engagementData = window.customerEngagement.exportData();
      this.customerData = {
        wishlistItems: engagementData.wishlist?.length || 0,
        recentlyViewed: engagementData.recentlyViewed?.length || 0,
        browsingHistory: engagementData.browsingHistory || {}
      };
    } else {
      this.customerData = {
        wishlistItems: 0,
        recentlyViewed: 0,
        browsingHistory: {}
      };
    }
  }

  updateMetrics() {
    const totalRevenue = this.salesData.reduce((sum, day) => sum + day.revenue, 0);
    const totalOrders = this.salesData.reduce((sum, day) => sum + day.orders, 0);
    const totalVisitors = this.salesData.reduce((sum, day) => sum + day.visitors, 0);
    const totalConversions = this.salesData.reduce((sum, day) => sum + day.conversions, 0);
    
    const conversionRate = totalVisitors > 0 ? (totalConversions / totalVisitors) * 100 : 0;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate period-over-period changes
    const midPoint = Math.floor(this.salesData.length / 2);
    const firstHalf = this.salesData.slice(0, midPoint);
    const secondHalf = this.salesData.slice(midPoint);
    
    const firstHalfRevenue = firstHalf.reduce((sum, day) => sum + day.revenue, 0);
    const secondHalfRevenue = secondHalf.reduce((sum, day) => sum + day.revenue, 0);
    const revenueChange = firstHalfRevenue > 0 ? ((secondHalfRevenue - firstHalfRevenue) / firstHalfRevenue) * 100 : 0;

    // Update DOM elements
    document.getElementById('total-revenue').textContent = `$${totalRevenue.toFixed(2)}`;
    document.getElementById('total-orders').textContent = totalOrders.toString();
    document.getElementById('conversion-rate').textContent = `${conversionRate.toFixed(1)}%`;
    document.getElementById('avg-order-value').textContent = `$${avgOrderValue.toFixed(2)}`;

    // Update change indicators
    this.updateChangeIndicator('revenue-change', revenueChange);
    this.updateChangeIndicator('orders-change', 0); // Calculate similar to revenue
    this.updateChangeIndicator('conversion-change', 0);
    this.updateChangeIndicator('aov-change', 0);
  }

  updateChangeIndicator(elementId, change) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const isPositive = change >= 0;
    element.textContent = `${isPositive ? '+' : ''}${change.toFixed(1)}%`;
    element.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
  }

  initializeChart() {
    const canvas = document.getElementById('revenue-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // Simple chart implementation (you could use Chart.js for more advanced charts)
    this.chart = {
      canvas: canvas,
      ctx: ctx,
      type: 'revenue'
    };
  }

  updateCharts() {
    this.updateRevenueChart();
    this.updateCategoriesChart();
  }

  updateRevenueChart() {
    const canvas = document.getElementById('revenue-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    if (this.salesData.length === 0) return;

    // Draw simple line chart
    const data = this.chart.type === 'revenue' 
      ? this.salesData.map(d => d.revenue)
      : this.salesData.map(d => d.orders);

    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue || 1;

    const stepX = width / (data.length - 1);
    const stepY = height - 40;

    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.beginPath();

    data.forEach((value, index) => {
      const x = index * stepX;
      const y = height - 20 - ((value - minValue) / range) * stepY;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw points
    ctx.fillStyle = '#3b82f6';
    data.forEach((value, index) => {
      const x = index * stepX;
      const y = height - 20 - ((value - minValue) / range) * stepY;
      
      ctx.beginPath();
      ctx.arc(x, y, 3, 0, 2 * Math.PI);
      ctx.fill();
    });
  }

  updateCategoriesChart() {
    const container = document.getElementById('categories-chart');
    if (!container) return;

    // Get category data from products
    const categoryData = this.getCategoryAnalytics();
    
    container.innerHTML = '';
    
    categoryData.forEach(category => {
      const bar = document.createElement('div');
      bar.className = 'category-bar';
      
      bar.innerHTML = `
        <div class="category-name">${category.name}</div>
        <div class="category-progress">
          <div class="category-fill" style="width: ${category.percentage}%"></div>
        </div>
        <div class="category-value">${category.count}</div>
      `;
      
      container.appendChild(bar);
    });
  }

  getCategoryAnalytics() {
    // This would typically come from your product data
    // For now, return mock data
    return [
      { name: 'Clothing', count: 45, percentage: 80 },
      { name: 'Books', count: 32, percentage: 60 },
      { name: 'Home Decor', count: 28, percentage: 50 },
      { name: 'Electronics', count: 15, percentage: 30 },
      { name: 'Toys', count: 12, percentage: 25 }
    ];
  }

  switchChart(type) {
    // Update active button
    document.querySelectorAll('.chart-toggle').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-chart="${type}"]`).classList.add('active');

    // Update chart
    this.chart.type = type;
    this.updateRevenueChart();
  }

  updateAnalytics() {
    this.updateTopProducts();
    this.updateSearchAnalytics();
    this.updateCustomerInsights();
    this.updatePerformanceMetrics();
  }

  updateTopProducts() {
    const container = document.getElementById('top-products-list');
    if (!container) return;

    // Mock top products data
    const topProducts = [
      { name: 'Vintage Leather Jacket', sales: 12, revenue: 480 },
      { name: 'Classic Novel Collection', sales: 8, revenue: 120 },
      { name: 'Handmade Ceramic Vase', sales: 6, revenue: 180 },
      { name: 'Retro Gaming Console', sales: 4, revenue: 320 },
      { name: 'Designer Handbag', sales: 3, revenue: 225 }
    ];

    container.innerHTML = '';
    
    topProducts.forEach(product => {
      const item = document.createElement('div');
      item.className = 'analytics-item';
      item.innerHTML = `
        <div class="item-name">${product.name}</div>
        <div class="item-value">${product.sales} sales</div>
      `;
      container.appendChild(item);
    });
  }

  updateSearchAnalytics() {
    const container = document.getElementById('search-analytics-list');
    if (!container) return;

    container.innerHTML = '';

    if (this.searchAnalytics.topQueries.length === 0) {
      container.innerHTML = '<div class="analytics-item"><div class="item-name">No search data available</div></div>';
      return;
    }

    this.searchAnalytics.topQueries.slice(0, 5).forEach(query => {
      const item = document.createElement('div');
      item.className = 'analytics-item';
      item.innerHTML = `
        <div class="item-name">"${query.query}"</div>
        <div class="item-value">${query.count} searches</div>
      `;
      container.appendChild(item);
    });
  }

  updateCustomerInsights() {
    const container = document.getElementById('customer-insights');
    if (!container) return;

    const insights = [
      { label: 'Wishlist Items', value: this.customerData.wishlistItems },
      { label: 'Recently Viewed', value: this.customerData.recentlyViewed },
      { label: 'Search CTR', value: `${(this.searchAnalytics.clickThroughRate * 100).toFixed(1)}%` },
      { label: 'Avg Session', value: '3.2 min' }
    ];

    container.innerHTML = '';
    
    insights.forEach(insight => {
      const item = document.createElement('div');
      item.className = 'analytics-item';
      item.innerHTML = `
        <div class="item-name">${insight.label}</div>
        <div class="item-value">${insight.value}</div>
      `;
      container.appendChild(item);
    });
  }

  updatePerformanceMetrics() {
    const container = document.getElementById('performance-metrics');
    if (!container) return;

    const metrics = [
      { label: 'Page Load Time', value: '1.2s', status: 'good' },
      { label: 'Mobile Score', value: '95/100', status: 'excellent' },
      { label: 'SEO Score', value: '88/100', status: 'good' },
      { label: 'Accessibility', value: '92/100', status: 'excellent' }
    ];

    container.innerHTML = '';
    
    metrics.forEach(metric => {
      const item = document.createElement('div');
      item.className = 'analytics-item';
      item.innerHTML = `
        <div class="item-name">${metric.label}</div>
        <div class="item-value">${metric.value}</div>
      `;
      container.appendChild(item);
    });
  }

  refreshDashboard() {
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
      refreshBtn.style.opacity = '0.5';
      refreshBtn.style.pointerEvents = 'none';
    }

    this.loadData().then(() => {
      if (refreshBtn) {
        refreshBtn.style.opacity = '1';
        refreshBtn.style.pointerEvents = 'auto';
      }
    });
  }

  exportSalesData(format) {
    const data = this.salesData.map(day => ({
      Date: day.date,
      Revenue: day.revenue,
      Orders: day.orders,
      Visitors: day.visitors,
      'Conversion Rate': ((day.conversions / day.visitors) * 100).toFixed(2) + '%'
    }));

    if (format === 'csv') {
      this.downloadCSV(data, 'sales-data.csv');
    } else {
      this.downloadJSON(data, 'sales-data.json');
    }
  }

  exportAnalyticsData(format) {
    const data = {
      period: this.currentPeriod,
      salesData: this.salesData,
      searchAnalytics: this.searchAnalytics,
      customerData: this.customerData,
      exportedAt: new Date().toISOString()
    };

    this.downloadJSON(data, 'analytics-data.json');
  }

  generateReport() {
    // Generate a comprehensive report
    const report = this.createReportHTML();
    const blob = new Blob([report], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-report-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  createReportHTML() {
    const totalRevenue = this.salesData.reduce((sum, day) => sum + day.revenue, 0);
    const totalOrders = this.salesData.reduce((sum, day) => sum + day.orders, 0);
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sales Report - Cheers Marketplace</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .header { text-align: center; margin-bottom: 40px; }
          .metric { display: inline-block; margin: 20px; text-align: center; }
          .metric-value { font-size: 2em; font-weight: bold; color: #2563eb; }
          .metric-label { color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          th { background-color: #f5f5f5; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Sales Report</h1>
          <p>Period: Last ${this.currentPeriod} days</p>
          <p>Generated: ${new Date().toLocaleDateString()}</p>
        </div>
        
        <div class="metrics">
          <div class="metric">
            <div class="metric-value">$${totalRevenue.toFixed(2)}</div>
            <div class="metric-label">Total Revenue</div>
          </div>
          <div class="metric">
            <div class="metric-value">${totalOrders}</div>
            <div class="metric-label">Total Orders</div>
          </div>
        </div>
        
        <h2>Daily Sales Data</h2>
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Revenue</th>
              <th>Orders</th>
              <th>Visitors</th>
              <th>Conversion Rate</th>
            </tr>
          </thead>
          <tbody>
            ${this.salesData.map(day => `
              <tr>
                <td>${day.date}</td>
                <td>$${day.revenue.toFixed(2)}</td>
                <td>${day.orders}</td>
                <td>${day.visitors}</td>
                <td>${((day.conversions / day.visitors) * 100).toFixed(1)}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
  }

  downloadCSV(data, filename) {
    const csv = this.convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  downloadJSON(data, filename) {
    const json = JSON.stringify(data, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');
    
    return csvContent;
  }

  showError(message) {
    console.error(message);
    // You could show a toast notification here
  }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('section-dashboard')) {
    window.adminSalesDashboard = new AdminSalesDashboard();
  }
});
