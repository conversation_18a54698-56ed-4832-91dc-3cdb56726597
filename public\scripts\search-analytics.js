/**
 * Search Analytics Implementation for Browser
 * Simplified version of the TypeScript analytics for client-side use
 */

class SearchAnalyticsManager {
  constructor() {
    this.events = [];
    this.STORAGE_KEY = 'cheers_search_analytics';
    this.MAX_EVENTS = 1000;
    this.sessionId = this.generateSessionId();
    this.loadFromStorage();
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  trackSearch(query, resultsCount, filters = {}) {
    const event = {
      id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim().toLowerCase(),
      timestamp: Date.now(),
      resultsCount,
      filters,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent
    };

    this.events.push(event);
    this.trimEvents();
    this.saveToStorage();

    return event.id;
  }

  trackResultClick(searchEventId, productId) {
    const event = this.events.find(e => e.id === searchEventId);
    if (event) {
      event.clickedResult = productId;
      this.saveToStorage();
    }
  }

  getAnalytics(days = 30) {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    const recentEvents = this.events.filter(e => e.timestamp >= cutoffTime);

    const totalSearches = recentEvents.length;
    const uniqueQueries = new Set(recentEvents.map(e => e.query)).size;
    const averageResultsPerSearch = totalSearches > 0 
      ? recentEvents.reduce((sum, e) => sum + e.resultsCount, 0) / totalSearches 
      : 0;

    // Top queries
    const queryStats = new Map();
    recentEvents.forEach(event => {
      const existing = queryStats.get(event.query) || { count: 0, totalResults: 0, clicks: 0 };
      existing.count++;
      existing.totalResults += event.resultsCount;
      if (event.clickedResult) existing.clicks++;
      queryStats.set(event.query, existing);
    });

    const topQueries = Array.from(queryStats.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgResults: stats.totalResults / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // No result queries
    const noResultQueries = Array.from(queryStats.entries())
      .filter(([_, stats]) => stats.totalResults === 0)
      .map(([query, stats]) => ({ query, count: stats.count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Click-through rate
    const searchesWithClicks = recentEvents.filter(e => e.clickedResult).length;
    const clickThroughRate = totalSearches > 0 ? searchesWithClicks / totalSearches : 0;

    // Popular filters
    const popularFilters = {};
    recentEvents.forEach(event => {
      if (event.filters) {
        Object.entries(event.filters).forEach(([key, value]) => {
          if (value) {
            const filterKey = `${key}:${value}`;
            popularFilters[filterKey] = (popularFilters[filterKey] || 0) + 1;
          }
        });
      }
    });

    // Search trends (daily)
    const trends = new Map();
    recentEvents.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      trends.set(date, (trends.get(date) || 0) + 1);
    });

    const searchTrends = Array.from(trends.entries())
      .map(([date, searches]) => ({ date, searches }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalSearches,
      uniqueQueries,
      averageResultsPerSearch,
      topQueries,
      noResultQueries,
      clickThroughRate,
      popularFilters,
      searchTrends
    };
  }

  getPopularSuggestions(prefix, limit = 5) {
    const analytics = this.getAnalytics(7);
    return analytics.topQueries
      .filter(q => q.query.startsWith(prefix.toLowerCase()))
      .map(q => q.query)
      .slice(0, limit);
  }

  exportData() {
    return [...this.events];
  }

  clearData() {
    this.events = [];
    this.saveToStorage();
  }

  loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load search analytics from storage:', error);
      this.events = [];
    }
  }

  saveToStorage() {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.events));
    } catch (error) {
      console.warn('Failed to save search analytics to storage:', error);
    }
  }

  trimEvents() {
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, this.MAX_EVENTS);
    }
  }
}

// Create global instance
window.searchAnalytics = new SearchAnalyticsManager();

// Global functions for easy access
window.trackSearch = function(query, resultsCount, filters) {
  return window.searchAnalytics.trackSearch(query, resultsCount, filters);
};

window.trackResultClick = function(searchEventId, productId) {
  window.searchAnalytics.trackResultClick(searchEventId, productId);
};

window.getSearchAnalytics = function(days) {
  return window.searchAnalytics.getAnalytics(days);
};

window.getPopularSuggestions = function(prefix, limit) {
  return window.searchAnalytics.getPopularSuggestions(prefix, limit);
};
