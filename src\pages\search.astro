---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import EnhancedSearchBox from '../components/search/EnhancedSearchBox.astro';
import OptimizedProductsList from '../components/OptimizedProductsList.astro';
import { generateSearchDescription } from '../utils/metaDescriptions.ts';

// Import products data
import products from '../data/products.json';

// Get search parameters from URL (will be handled by client-side JS)
const searchQuery = '';
const metaDescription = generateSearchDescription(searchQuery || 'products');
const pageTitle = searchQuery 
  ? `Search Results for "${searchQuery}" | Cheers Marketplace`
  : "Search Products | Cheers Marketplace";
---

<Layout
  title={pageTitle}
  description={metaDescription}
>
  <Header />
  
  <main class="search-page">
    <!-- Search Hero Section -->
    <section class="search-hero">
      <div class="container">
        <div class="search-hero-content">
          <h1>Search Our Products</h1>
          <p class="search-subtitle">Find exactly what you're looking for with our advanced search and filtering options</p>
          
          <!-- Enhanced Search Component -->
          <EnhancedSearchBox 
            placeholder="Search for products, categories, or keywords..."
            showSuggestions={true}
            showFilters={true}
            className="hero-search"
          />
        </div>
      </div>
    </section>

    <!-- Search Results Section -->
    <section class="search-results">
      <div class="container">
        <!-- Search Analytics Summary -->
        <div class="search-analytics" id="search-analytics" style="display: none;">
          <div class="analytics-card">
            <h3>Search Insights</h3>
            <div class="analytics-grid">
              <div class="metric">
                <span class="metric-value" id="total-searches">0</span>
                <span class="metric-label">Total Searches</span>
              </div>
              <div class="metric">
                <span class="metric-value" id="avg-results">0</span>
                <span class="metric-label">Avg Results</span>
              </div>
              <div class="metric">
                <span class="metric-value" id="click-rate">0%</span>
                <span class="metric-label">Click Rate</span>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results Message -->
        <div class="no-results" id="no-results" style="display: none;">
          <div class="no-results-content">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
              <line x1="11" y1="8" x2="11" y2="12"></line>
              <line x1="11" y1="16" x2="11.01" y2="16"></line>
            </svg>
            <h3>No products found</h3>
            <p>Try adjusting your search terms or filters to find what you're looking for.</p>
            
            <div class="search-suggestions">
              <h4>Popular searches:</h4>
              <div class="suggestion-tags" id="popular-suggestions">
                <!-- Popular suggestions will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Search Results Grid -->
        <div class="search-results-grid" id="search-results-grid">
          <!-- Initial load shows all products -->
          <OptimizedProductsList 
            products={products}
            showFilters={false}
            className="search-results-list"
          />
        </div>

        <!-- Load More Button -->
        <div class="load-more-container" id="load-more-container" style="display: none;">
          <button class="load-more-btn" id="load-more-btn">
            Load More Products
          </button>
        </div>
      </div>
    </section>

    <!-- Search Tips Section -->
    <section class="search-tips">
      <div class="container">
        <div class="tips-content">
          <h2>Search Tips</h2>
          <div class="tips-grid">
            <div class="tip-card">
              <div class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </div>
              <h3>Use Keywords</h3>
              <p>Search by product name, brand, category, or description keywords for best results.</p>
            </div>
            
            <div class="tip-card">
              <div class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 6h18l-2 13H5L3 6z"></path>
                  <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg>
              </div>
              <h3>Filter Results</h3>
              <p>Use price range, condition, and category filters to narrow down your search.</p>
            </div>
            
            <div class="tip-card">
              <div class="tip-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <h3>Sort Options</h3>
              <p>Sort by relevance, price, newest arrivals, or alphabetically to find what you need.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<style>
  .search-page {
    min-height: 100vh;
  }

  .search-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 3rem;
  }

  .search-hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .search-hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .search-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }

  .hero-search {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .search-results {
    padding: 3rem 0;
  }

  .search-analytics {
    margin-bottom: 2rem;
  }

  .analytics-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
  }

  .analytics-card h3 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }

  .metric {
    text-align: center;
  }

  .metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
  }

  .metric-label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }

  .no-results {
    text-align: center;
    padding: 4rem 2rem;
  }

  .no-results-content {
    max-width: 400px;
    margin: 0 auto;
  }

  .no-results svg {
    color: #9ca3af;
    margin-bottom: 1rem;
  }

  .no-results h3 {
    font-size: 1.5rem;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .no-results p {
    color: #6b7280;
    margin-bottom: 2rem;
  }

  .search-suggestions h4 {
    font-size: 1rem;
    color: #374151;
    margin-bottom: 1rem;
  }

  .suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }

  .suggestion-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .suggestion-tag:hover {
    background: #e5e7eb;
    color: #1f2937;
  }

  .search-results-grid {
    margin-bottom: 2rem;
  }

  .load-more-container {
    text-align: center;
  }

  .load-more-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .load-more-btn:hover {
    background: #1d4ed8;
  }

  .search-tips {
    background: #f9fafb;
    padding: 4rem 0;
  }

  .tips-content {
    text-align: center;
  }

  .tips-content h2 {
    font-size: 2rem;
    color: #374151;
    margin-bottom: 3rem;
  }

  .tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
  }

  .tip-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
  }

  .tip-icon {
    background: #dbeafe;
    color: #2563eb;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
  }

  .tip-card h3 {
    font-size: 1.25rem;
    color: #374151;
    margin-bottom: 0.75rem;
  }

  .tip-card p {
    color: #6b7280;
    line-height: 1.6;
  }

  @media (max-width: 768px) {
    .search-hero {
      padding: 2rem 0;
    }

    .search-hero h1 {
      font-size: 2rem;
    }

    .hero-search {
      padding: 1.5rem;
    }

    .analytics-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .tips-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<!-- Load search analytics and enhanced search functionality -->
<script src="/scripts/search-analytics.js" is:inline></script>
<script src="/scripts/enhanced-search.js" is:inline></script>

<script>
  // Initialize search page functionality
  document.addEventListener('DOMContentLoaded', () => {
    // Load search analytics if available
    if (typeof getSearchAnalytics === 'function') {
      const analytics = getSearchAnalytics(30);
      
      // Update analytics display
      document.getElementById('total-searches').textContent = analytics.totalSearches;
      document.getElementById('avg-results').textContent = analytics.averageResultsPerSearch.toFixed(1);
      document.getElementById('click-rate').textContent = (analytics.clickThroughRate * 100).toFixed(1) + '%';
      
      // Show analytics if there's data
      if (analytics.totalSearches > 0) {
        document.getElementById('search-analytics').style.display = 'block';
      }
      
      // Populate popular suggestions
      const suggestionsContainer = document.getElementById('popular-suggestions');
      analytics.topQueries.slice(0, 6).forEach(query => {
        const tag = document.createElement('span');
        tag.className = 'suggestion-tag';
        tag.textContent = query.query;
        tag.addEventListener('click', () => {
          const searchInput = document.getElementById('enhanced-search-input');
          if (searchInput) {
            searchInput.value = query.query;
            searchInput.dispatchEvent(new Event('input'));
          }
        });
        suggestionsContainer.appendChild(tag);
      });
    }

    // Listen for search results updates
    document.addEventListener('searchResultsUpdated', (e) => {
      const { products, query } = e.detail;
      
      // Show/hide no results message
      const noResults = document.getElementById('no-results');
      const resultsGrid = document.getElementById('search-results-grid');
      
      if (products.length === 0 && query) {
        noResults.style.display = 'block';
        resultsGrid.style.display = 'none';
      } else {
        noResults.style.display = 'none';
        resultsGrid.style.display = 'block';
      }
      
      // Update product display
      updateProductDisplay(products);
    });

    function updateProductDisplay(products) {
      // This would integrate with your existing product list component
      // For now, we'll dispatch an event that the OptimizedProductsList can listen to
      document.dispatchEvent(new CustomEvent('updateProductList', {
        detail: { products }
      }));
    }
  });
</script>
