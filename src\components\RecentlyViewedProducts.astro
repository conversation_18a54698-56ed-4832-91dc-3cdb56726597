---
// Recently Viewed Products Component
export interface Props {
  title?: string;
  limit?: number;
  showTitle?: boolean;
  className?: string;
  layout?: 'horizontal' | 'grid';
}

const {
  title = "Recently Viewed",
  limit = 8,
  showTitle = true,
  className = "",
  layout = "horizontal"
} = Astro.props;
---

<section class={`recently-viewed-section ${className}`} id="recently-viewed-section" style="display: none;">
  {showTitle && (
    <div class="section-header">
      <h2 class="section-title">{title}</h2>
      <button class="clear-history-btn" id="clear-recently-viewed" type="button" aria-label="Clear recently viewed history">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 6h18"></path>
          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
        </svg>
        Clear History
      </button>
    </div>
  )}

  <div class={`recently-viewed-container ${layout}`}>
    <div class="recently-viewed-grid" id="recently-viewed-grid">
      <!-- Products will be populated by JavaScript -->
    </div>
    
    <!-- Navigation arrows for horizontal layout -->
    {layout === 'horizontal' && (
      <div class="navigation-controls">
        <button class="nav-btn prev-btn" id="recently-viewed-prev" aria-label="Previous products">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
        </button>
        <button class="nav-btn next-btn" id="recently-viewed-next" aria-label="Next products">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </button>
      </div>
    )}
  </div>

  <!-- Empty state -->
  <div class="empty-state" id="recently-viewed-empty" style="display: none;">
    <div class="empty-content">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
      </svg>
      <h3>No recently viewed products</h3>
      <p>Products you view will appear here for easy access.</p>
      <a href="/products/" class="browse-btn">Browse Products</a>
    </div>
  </div>
</section>

<style>
  .recently-viewed-section {
    margin: 2rem 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .clear-history-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .clear-history-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
  }

  .recently-viewed-container {
    position: relative;
  }

  .recently-viewed-container.horizontal {
    overflow: hidden;
  }

  .recently-viewed-grid {
    display: grid;
    gap: 1rem;
    transition: transform 0.3s ease;
  }

  .recently-viewed-container.horizontal .recently-viewed-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-auto-flow: column;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .recently-viewed-container.horizontal .recently-viewed-grid::-webkit-scrollbar {
    display: none;
  }

  .recently-viewed-container.grid .recently-viewed-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .product-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
  }

  .product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }

  .product-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: #f3f4f6;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-price {
    font-weight: 600;
    color: #059669;
    font-size: 1rem;
  }

  .product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
  }

  .view-count {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .viewed-time {
    font-style: italic;
  }

  .navigation-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 2;
  }

  .nav-btn {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    pointer-events: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .nav-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: scale(1.05);
  }

  .nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .nav-btn.prev-btn {
    left: -20px;
  }

  .nav-btn.next-btn {
    right: -20px;
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
  }

  .empty-content svg {
    margin-bottom: 1rem;
    color: #9ca3af;
  }

  .empty-content h3 {
    font-size: 1.125rem;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .empty-content p {
    margin-bottom: 1.5rem;
  }

  .browse-btn {
    display: inline-flex;
    align-items: center;
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .browse-btn:hover {
    background: #1d4ed8;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .recently-viewed-container.horizontal .recently-viewed-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }

    .recently-viewed-container.grid .recently-viewed-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .product-image {
      height: 120px;
    }

    .product-info {
      padding: 0.75rem;
    }

    .nav-btn {
      width: 36px;
      height: 36px;
    }

    .nav-btn.prev-btn {
      left: -18px;
    }

    .nav-btn.next-btn {
      right: -18px;
    }
  }

  @media (max-width: 480px) {
    .recently-viewed-container.horizontal .recently-viewed-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .recently-viewed-container.grid .recently-viewed-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
  }

  /* Loading state */
  .product-card.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .product-card {
      border-width: 2px;
    }

    .nav-btn {
      border-width: 2px;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .product-card,
    .nav-btn,
    .recently-viewed-grid {
      transition: none;
    }

    .product-card:hover {
      transform: none;
    }

    .nav-btn:hover {
      transform: none;
    }
  }
</style>

<script>
  // Recently viewed functionality will be handled by the customer engagement script
  // This component provides the structure and styling
</script>
