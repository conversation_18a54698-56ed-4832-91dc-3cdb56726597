---
// Admin Sales Dashboard Component
// Comprehensive analytics and insights for the admin panel
---

<div class="admin-section" id="section-dashboard">
  <div class="dashboard-header">
    <h2>Sales Dashboard</h2>
    <div class="dashboard-controls">
      <select id="dashboard-period" class="admin-select">
        <option value="7">Last 7 days</option>
        <option value="30" selected>Last 30 days</option>
        <option value="90">Last 90 days</option>
        <option value="365">Last year</option>
      </select>
      <button id="refresh-dashboard" class="btn-refresh" title="Refresh dashboard data">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"></polyline>
          <polyline points="1,20 1,14 7,14"></polyline>
          <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Key Metrics Cards -->
  <div class="metrics-grid">
    <div class="metric-card">
      <div class="metric-icon revenue">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
        </svg>
      </div>
      <div class="metric-content">
        <div class="metric-value" id="total-revenue">$0</div>
        <div class="metric-label">Total Revenue</div>
        <div class="metric-change" id="revenue-change">+0%</div>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon orders">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19,7H16V6A4,4 0 0,0 12,2A4,4 0 0,0 8,6V7H5A1,1 0 0,0 4,8V19A3,3 0 0,0 7,22H17A3,3 0 0,0 20,19V8A1,1 0 0,0 19,7M10,6A2,2 0 0,1 12,4A2,2 0 0,1 14,6V7H10V6M18,19A1,1 0 0,1 17,20H7A1,1 0 0,1 6,19V9H8V10A1,1 0 0,0 9,11A1,1 0 0,0 10,10V9H14V10A1,1 0 0,0 15,11A1,1 0 0,0 16,10V9H18V19Z"/>
        </svg>
      </div>
      <div class="metric-content">
        <div class="metric-value" id="total-orders">0</div>
        <div class="metric-label">Total Orders</div>
        <div class="metric-change" id="orders-change">+0%</div>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon conversion">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
        </svg>
      </div>
      <div class="metric-content">
        <div class="metric-value" id="conversion-rate">0%</div>
        <div class="metric-label">Conversion Rate</div>
        <div class="metric-change" id="conversion-change">+0%</div>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon avg-order">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
        </svg>
      </div>
      <div class="metric-content">
        <div class="metric-value" id="avg-order-value">$0</div>
        <div class="metric-label">Avg Order Value</div>
        <div class="metric-change" id="aov-change">+0%</div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="charts-section">
    <div class="chart-container">
      <div class="chart-header">
        <h3>Revenue Trend</h3>
        <div class="chart-controls">
          <button class="chart-toggle active" data-chart="revenue">Revenue</button>
          <button class="chart-toggle" data-chart="orders">Orders</button>
        </div>
      </div>
      <div class="chart-content">
        <canvas id="revenue-chart" width="400" height="200"></canvas>
      </div>
    </div>

    <div class="chart-container">
      <div class="chart-header">
        <h3>Top Categories</h3>
      </div>
      <div class="chart-content">
        <div id="categories-chart" class="categories-chart">
          <!-- Category bars will be populated by JavaScript -->
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Tables -->
  <div class="analytics-section">
    <div class="analytics-grid">
      <!-- Top Products -->
      <div class="analytics-card">
        <div class="analytics-header">
          <h3>Top Selling Products</h3>
          <span class="analytics-period" id="top-products-period">Last 30 days</span>
        </div>
        <div class="analytics-content">
          <div id="top-products-list" class="analytics-list">
            <!-- Top products will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Search Analytics -->
      <div class="analytics-card">
        <div class="analytics-header">
          <h3>Search Analytics</h3>
          <span class="analytics-period" id="search-period">Last 30 days</span>
        </div>
        <div class="analytics-content">
          <div id="search-analytics-list" class="analytics-list">
            <!-- Search analytics will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Customer Insights -->
      <div class="analytics-card">
        <div class="analytics-header">
          <h3>Customer Insights</h3>
          <span class="analytics-period" id="customer-period">Last 30 days</span>
        </div>
        <div class="analytics-content">
          <div id="customer-insights" class="insights-grid">
            <!-- Customer insights will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="analytics-card">
        <div class="analytics-header">
          <h3>Performance Metrics</h3>
          <span class="analytics-period" id="performance-period">Last 30 days</span>
        </div>
        <div class="analytics-content">
          <div id="performance-metrics" class="metrics-list">
            <!-- Performance metrics will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Export Section -->
  <div class="export-section">
    <div class="export-header">
      <h3>Export Data</h3>
      <p>Download analytics data for external analysis</p>
    </div>
    <div class="export-actions">
      <button id="export-sales-csv" class="btn-export">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        Export Sales Data (CSV)
      </button>
      <button id="export-analytics-json" class="btn-export">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5,3H7V5H5V10A2,2 0 0,1 3,8V6A2,2 0 0,1 5,4V3M19,3V4A2,2 0 0,1 21,6V8A2,2 0 0,1 19,10V5H17V3H19M16,19H14V21H16V20A2,2 0 0,0 18,18V16A2,2 0 0,0 16,14V19M10,19V14A2,2 0 0,0 8,16V18A2,2 0 0,0 10,20V21H12V19H10Z"/>
        </svg>
        Export Analytics (JSON)
      </button>
      <button id="generate-report" class="btn-export primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
        </svg>
        Generate Report
      </button>
    </div>
  </div>
</div>

<style>
  .admin-section#section-dashboard {
    padding: 1.5rem;
  }

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .dashboard-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .btn-refresh {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
  }

  .btn-refresh:hover {
    background: #f9fafb;
    color: #374151;
  }

  /* Metrics Grid */
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .metric-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
  }

  .metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .metric-icon.revenue { background: #10b981; }
  .metric-icon.orders { background: #3b82f6; }
  .metric-icon.conversion { background: #8b5cf6; }
  .metric-icon.avg-order { background: #f59e0b; }

  .metric-content {
    flex: 1;
  }

  .metric-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
  }

  .metric-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0.25rem 0;
  }

  .metric-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
  }

  .metric-change.positive {
    background: #dcfce7;
    color: #166534;
  }

  .metric-change.negative {
    background: #fef2f2;
    color: #dc2626;
  }

  /* Charts Section */
  .charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .chart-container {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .chart-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }

  .chart-controls {
    display: flex;
    gap: 0.5rem;
  }

  .chart-toggle {
    background: #f3f4f6;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .chart-toggle.active {
    background: #2563eb;
    color: white;
  }

  .chart-content {
    height: 200px;
    position: relative;
  }

  .categories-chart {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .category-bar {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .category-name {
    min-width: 80px;
    font-size: 0.875rem;
    color: #374151;
  }

  .category-progress {
    flex: 1;
    height: 8px;
    background: #f3f4f6;
    border-radius: 4px;
    overflow: hidden;
  }

  .category-fill {
    height: 100%;
    background: #3b82f6;
    transition: width 0.3s ease;
  }

  .category-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
    min-width: 40px;
    text-align: right;
  }

  /* Analytics Section */
  .analytics-section {
    margin-bottom: 2rem;
  }

  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .analytics-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
  }

  .analytics-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .analytics-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
  }

  .analytics-period {
    font-size: 0.75rem;
    color: #6b7280;
  }

  .analytics-content {
    padding: 1.5rem;
  }

  .analytics-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .analytics-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .analytics-item:last-child {
    border-bottom: none;
  }

  .item-name {
    font-size: 0.875rem;
    color: #374151;
    flex: 1;
  }

  .item-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
  }

  /* Export Section */
  .export-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .export-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }

  .export-header p {
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .export-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .btn-export {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-export:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  .btn-export.primary {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
  }

  .btn-export.primary:hover {
    background: #1d4ed8;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .metrics-grid {
      grid-template-columns: 1fr;
    }

    .charts-section {
      grid-template-columns: 1fr;
    }

    .analytics-grid {
      grid-template-columns: 1fr;
    }

    .export-actions {
      flex-direction: column;
    }
  }
</style>
