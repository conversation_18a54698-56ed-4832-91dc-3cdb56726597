/**
 * Customer Engagement System for Browser
 * Handles wishlist, recently viewed, and personalization features
 */

class CustomerEngagementManager {
  constructor() {
    this.WISHLIST_KEY = 'cheers_wishlist';
    this.RECENTLY_VIEWED_KEY = 'cheers_recently_viewed';
    this.BROWSING_HISTORY_KEY = 'cheers_browsing_history';
    this.MAX_RECENTLY_VIEWED = 20;
    this.MAX_SEARCH_TERMS = 50;
    
    this.init();
  }

  init() {
    this.bindWishlistEvents();
    this.updateWishlistButtons();
    this.trackPageView();
    this.initializeComponents();
  }

  initializeComponents() {
    // Initialize recently viewed component
    this.updateRecentlyViewedDisplay();

    // Initialize recommendations
    this.updateRecommendationsDisplay();

    // Bind component events
    this.bindComponentEvents();
  }

  bindComponentEvents() {
    // Clear recently viewed history
    const clearBtn = document.getElementById('clear-recently-viewed');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearRecentlyViewed();
      });
    }

    // Refresh recommendations buttons
    document.querySelectorAll('[id^="refresh-"]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const type = e.target.id.replace('refresh-', '');
        this.refreshRecommendations(type);
      });
    });

    // Navigation buttons for horizontal scrolling
    this.bindNavigationEvents();
  }

  bindNavigationEvents() {
    // Recently viewed navigation
    const recentlyViewedPrev = document.getElementById('recently-viewed-prev');
    const recentlyViewedNext = document.getElementById('recently-viewed-next');
    const recentlyViewedGrid = document.getElementById('recently-viewed-grid');

    if (recentlyViewedPrev && recentlyViewedNext && recentlyViewedGrid) {
      recentlyViewedPrev.addEventListener('click', () => {
        this.scrollHorizontal(recentlyViewedGrid, -200);
      });

      recentlyViewedNext.addEventListener('click', () => {
        this.scrollHorizontal(recentlyViewedGrid, 200);
      });
    }

    // Recommendations navigation
    document.querySelectorAll('[id$="-prev"], [id$="-next"]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const direction = e.target.id.includes('-prev') ? -200 : 200;
        const gridId = e.target.id.replace('-prev', '-grid').replace('-next', '-grid');
        const grid = document.getElementById(gridId);
        if (grid) {
          this.scrollHorizontal(grid, direction);
        }
      });
    });
  }

  scrollHorizontal(element, distance) {
    element.scrollBy({
      left: distance,
      behavior: 'smooth'
    });
  }

  // Wishlist Management
  addToWishlist(productId, notifyOnSale = false) {
    try {
      const wishlist = this.getWishlist();
      
      if (wishlist.some(item => item.productId === productId)) {
        return false;
      }
      
      const newItem = {
        productId,
        addedAt: Date.now(),
        notifyOnSale
      };
      
      wishlist.push(newItem);
      this.saveWishlist(wishlist);
      this.updateWishlistButtons();
      this.trackEvent('wishlist_add', { productId });
      this.showToast('Added to wishlist!', 'success');
      
      return true;
    } catch (error) {
      console.error('Failed to add to wishlist:', error);
      this.showToast('Failed to add to wishlist', 'error');
      return false;
    }
  }

  removeFromWishlist(productId) {
    try {
      const wishlist = this.getWishlist();
      const filtered = wishlist.filter(item => item.productId !== productId);
      
      if (filtered.length === wishlist.length) {
        return false;
      }
      
      this.saveWishlist(filtered);
      this.updateWishlistButtons();
      this.trackEvent('wishlist_remove', { productId });
      this.showToast('Removed from wishlist', 'info');
      
      return true;
    } catch (error) {
      console.error('Failed to remove from wishlist:', error);
      this.showToast('Failed to remove from wishlist', 'error');
      return false;
    }
  }

  isInWishlist(productId) {
    const wishlist = this.getWishlist();
    return wishlist.some(item => item.productId === productId);
  }

  getWishlist() {
    try {
      const stored = localStorage.getItem(this.WISHLIST_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load wishlist:', error);
      return [];
    }
  }

  saveWishlist(wishlist) {
    try {
      localStorage.setItem(this.WISHLIST_KEY, JSON.stringify(wishlist));
    } catch (error) {
      console.error('Failed to save wishlist:', error);
    }
  }

  // Recently Viewed Management
  addToRecentlyViewed(productId) {
    try {
      const recentlyViewed = this.getRecentlyViewed();
      const filtered = recentlyViewed.filter(item => item.productId !== productId);
      
      const newItem = {
        productId,
        viewedAt: Date.now(),
        viewCount: 1
      };
      
      const existing = recentlyViewed.find(item => item.productId === productId);
      if (existing) {
        newItem.viewCount = existing.viewCount + 1;
      }
      
      filtered.unshift(newItem);
      const trimmed = filtered.slice(0, this.MAX_RECENTLY_VIEWED);
      
      this.saveRecentlyViewed(trimmed);
      this.trackEvent('product_view', { productId });
      
    } catch (error) {
      console.error('Failed to add to recently viewed:', error);
    }
  }

  getRecentlyViewed() {
    try {
      const stored = localStorage.getItem(this.RECENTLY_VIEWED_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load recently viewed:', error);
      return [];
    }
  }

  saveRecentlyViewed(recentlyViewed) {
    try {
      localStorage.setItem(this.RECENTLY_VIEWED_KEY, JSON.stringify(recentlyViewed));
    } catch (error) {
      console.error('Failed to save recently viewed:', error);
    }
  }

  // Browsing History & Personalization
  updateBrowsingHistory(data) {
    try {
      const history = this.getBrowsingHistory();
      
      if (data.category) {
        history.categories[data.category] = (history.categories[data.category] || 0) + 1;
      }
      
      if (data.priceRange) {
        history.priceRanges[data.priceRange] = (history.priceRanges[data.priceRange] || 0) + 1;
      }
      
      if (data.condition) {
        history.conditions[data.condition] = (history.conditions[data.condition] || 0) + 1;
      }
      
      if (data.searchTerm && data.searchTerm.trim()) {
        const term = data.searchTerm.trim().toLowerCase();
        if (!history.searchTerms.includes(term)) {
          history.searchTerms.unshift(term);
          history.searchTerms = history.searchTerms.slice(0, this.MAX_SEARCH_TERMS);
        }
      }
      
      history.lastActivity = Date.now();
      this.saveBrowsingHistory(history);
      
    } catch (error) {
      console.error('Failed to update browsing history:', error);
    }
  }

  getBrowsingHistory() {
    try {
      const stored = localStorage.getItem(this.BROWSING_HISTORY_KEY);
      return stored ? JSON.parse(stored) : {
        categories: {},
        priceRanges: {},
        conditions: {},
        searchTerms: [],
        lastActivity: Date.now()
      };
    } catch (error) {
      console.error('Failed to load browsing history:', error);
      return {
        categories: {},
        priceRanges: {},
        conditions: {},
        searchTerms: [],
        lastActivity: Date.now()
      };
    }
  }

  saveBrowsingHistory(history) {
    try {
      localStorage.setItem(this.BROWSING_HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Failed to save browsing history:', error);
    }
  }

  // Event Binding
  bindWishlistEvents() {
    document.addEventListener('click', (e) => {
      const wishlistBtn = e.target.closest('.wishlist-btn');
      if (wishlistBtn) {
        e.preventDefault();
        this.handleWishlistClick(wishlistBtn);
      }
    });
  }

  handleWishlistClick(button) {
    const productId = button.dataset.productId;
    const productName = button.dataset.productName;
    
    if (!productId) return;
    
    const isInWishlist = this.isInWishlist(productId);
    const notifyOption = button.parentElement.querySelector('.notify-option');
    const notifyInput = notifyOption?.querySelector('.notify-input');
    
    if (isInWishlist) {
      this.removeFromWishlist(productId);
      if (notifyOption) {
        notifyOption.style.display = 'none';
      }
    } else {
      const notifyOnSale = notifyInput?.checked || false;
      this.addToWishlist(productId, notifyOnSale);
      
      // Show notify option if available
      if (notifyOption) {
        notifyOption.style.display = 'block';
        setTimeout(() => {
          notifyOption.style.display = 'none';
        }, 3000);
      }
    }
    
    // Add animation
    button.classList.add(isInWishlist ? 'removing' : 'adding');
    setTimeout(() => {
      button.classList.remove('adding', 'removing');
    }, 300);
  }

  updateWishlistButtons() {
    const buttons = document.querySelectorAll('.wishlist-btn');
    buttons.forEach(button => {
      const productId = button.dataset.productId;
      if (productId) {
        const isInWishlist = this.isInWishlist(productId);
        button.classList.toggle('in-wishlist', isInWishlist);
        
        // Update aria-label
        const productName = button.dataset.productName || 'item';
        button.setAttribute('aria-label', 
          isInWishlist ? `Remove ${productName} from wishlist` : `Add ${productName} to wishlist`
        );
      }
    });
  }

  // Page Tracking
  trackPageView() {
    const path = window.location.pathname;
    
    // Track product page views
    if (path.includes('/products/') && !path.includes('/category/')) {
      const productId = this.extractProductIdFromPath(path);
      if (productId) {
        this.addToRecentlyViewed(productId);
      }
    }
    
    // Track category browsing
    if (path.includes('/products/category/')) {
      const category = this.extractCategoryFromPath(path);
      if (category) {
        this.updateBrowsingHistory({ category });
      }
    }
  }

  extractProductIdFromPath(path) {
    // Extract product ID from URL - this depends on your URL structure
    // For slug-based URLs, you might need to map slug to ID
    const segments = path.split('/');
    const productSlug = segments[segments.length - 1] || segments[segments.length - 2];
    
    // If you have a global products array, find by slug
    if (window.productsData) {
      const product = window.productsData.find(p => p.slug === productSlug);
      return product?.id;
    }
    
    return null;
  }

  extractCategoryFromPath(path) {
    const segments = path.split('/');
    const categoryIndex = segments.indexOf('category');
    return categoryIndex >= 0 ? segments[categoryIndex + 1] : null;
  }

  // Utility Functions
  trackEvent(eventName, data) {
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'Customer Engagement',
        ...data
      });
    }
  }

  showToast(message, type = 'info') {
    // Integration with your toast notification system
    if (window.showToast) {
      window.showToast(message, type);
    } else {
      console.log(`Toast: ${message} (${type})`);
    }
  }

  // Data Management
  exportData() {
    return {
      wishlist: this.getWishlist(),
      recentlyViewed: this.getRecentlyViewed(),
      browsingHistory: this.getBrowsingHistory()
    };
  }

  importData(data) {
    try {
      if (data.wishlist) {
        this.saveWishlist(data.wishlist);
      }
      if (data.recentlyViewed) {
        this.saveRecentlyViewed(data.recentlyViewed);
      }
      if (data.browsingHistory) {
        this.saveBrowsingHistory(data.browsingHistory);
      }
      this.updateWishlistButtons();
    } catch (error) {
      console.error('Failed to import data:', error);
    }
  }

  clearAllData() {
    try {
      localStorage.removeItem(this.WISHLIST_KEY);
      localStorage.removeItem(this.RECENTLY_VIEWED_KEY);
      localStorage.removeItem(this.BROWSING_HISTORY_KEY);
      this.updateWishlistButtons();
      this.updateRecentlyViewedDisplay();
      this.updateRecommendationsDisplay();
    } catch (error) {
      console.error('Failed to clear data:', error);
    }
  }

  // Component Display Methods
  updateRecentlyViewedDisplay() {
    const section = document.getElementById('recently-viewed-section');
    const grid = document.getElementById('recently-viewed-grid');
    const empty = document.getElementById('recently-viewed-empty');

    if (!section || !grid) return;

    const recentlyViewed = this.getRecentlyViewed();

    if (recentlyViewed.length === 0) {
      section.style.display = 'none';
      if (empty) empty.style.display = 'block';
      return;
    }

    section.style.display = 'block';
    if (empty) empty.style.display = 'none';

    // Load products data
    this.loadProductsData().then(products => {
      if (!products) return;

      const recentProducts = this.getRecentlyViewedProductsFromData(products, recentlyViewed);
      this.renderRecentlyViewed(grid, recentProducts, recentlyViewed);
    });
  }

  updateRecommendationsDisplay() {
    const sections = document.querySelectorAll('[id^="recommendations-"]');

    sections.forEach(section => {
      const type = section.id.replace('recommendations-', '');
      this.updateRecommendationSection(type);
    });
  }

  updateRecommendationSection(type) {
    const section = document.getElementById(`recommendations-${type}`);
    const grid = document.getElementById(`recommendations-${type}-grid`);
    const loading = document.getElementById(`recommendations-${type}-loading`);
    const empty = document.getElementById(`recommendations-${type}-empty`);
    const insights = document.getElementById(`recommendations-${type}-insights`);

    if (!section || !grid) return;

    // Show loading
    if (loading) loading.style.display = 'block';
    if (empty) empty.style.display = 'none';
    grid.style.display = 'none';

    this.loadProductsData().then(products => {
      if (!products) {
        if (loading) loading.style.display = 'none';
        if (empty) empty.style.display = 'block';
        return;
      }

      const recommendations = this.getRecommendationsByType(type, products);

      if (loading) loading.style.display = 'none';

      if (recommendations.length === 0) {
        section.style.display = 'none';
        if (empty) empty.style.display = 'block';
        return;
      }

      section.style.display = 'block';
      grid.style.display = 'grid';
      if (empty) empty.style.display = 'none';

      this.renderRecommendations(grid, recommendations, type);
      this.updateRecommendationInsights(insights, type);
    });
  }

  getRecommendationsByType(type, products) {
    switch (type) {
      case 'browsing':
        return this.getPersonalizedRecommendations(products, 8);
      case 'similar':
        return this.getSimilarProducts(products, 8);
      case 'trending':
        return this.getTrendingProducts(products, 8);
      default:
        return [];
    }
  }

  getPersonalizedRecommendations(allProducts, limit = 8) {
    const history = this.getBrowsingHistory();
    const recentlyViewed = this.getRecentlyViewed();
    const wishlist = this.getWishlist();

    const excludedIds = new Set([
      ...recentlyViewed.map(item => item.productId),
      ...wishlist.map(item => item.productId)
    ]);

    const scoredProducts = allProducts
      .filter(product => !excludedIds.has(product.id))
      .map(product => {
        let score = 0;

        const categoryScore = history.categories[product.category] || 0;
        score += categoryScore * 0.4;

        const conditionScore = history.conditions[product.condition] || 0;
        score += conditionScore * 0.2;

        const priceRange = this.getPriceRange(product.price);
        const priceScore = history.priceRanges[priceRange] || 0;
        score += priceScore * 0.2;

        const searchScore = this.calculateSearchRelevance(product, history.searchTerms);
        score += searchScore * 0.2;

        return { product, score };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.product);

    return scoredProducts;
  }

  getSimilarProducts(allProducts, limit = 8) {
    const recentlyViewed = this.getRecentlyViewed();
    if (recentlyViewed.length === 0) return [];

    const lastViewed = recentlyViewed[0];
    const lastProduct = allProducts.find(p => p.id === lastViewed.productId);
    if (!lastProduct) return [];

    return allProducts
      .filter(p => p.id !== lastProduct.id && p.category === lastProduct.category)
      .slice(0, limit);
  }

  getTrendingProducts(allProducts, limit = 8) {
    // Simple trending based on recent creation date
    return allProducts
      .sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0))
      .slice(0, limit);
  }

  getPriceRange(price) {
    if (price < 10) return '0-10';
    if (price < 25) return '10-25';
    if (price < 50) return '25-50';
    if (price < 100) return '50-100';
    return '100+';
  }

  calculateSearchRelevance(product, searchTerms) {
    let relevance = 0;
    const productText = `${product.name} ${product.category}`.toLowerCase();

    searchTerms.forEach(term => {
      if (productText.includes(term)) {
        relevance += 1;
      }
    });

    return relevance;
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.customerEngagement = new CustomerEngagementManager();
});

// Global convenience functions
window.addToWishlist = function(productId, notifyOnSale) {
  return window.customerEngagement?.addToWishlist(productId, notifyOnSale);
};

window.removeFromWishlist = function(productId) {
  return window.customerEngagement?.removeFromWishlist(productId);
};

window.isInWishlist = function(productId) {
  return window.customerEngagement?.isInWishlist(productId);
};

window.addToRecentlyViewed = function(productId) {
  return window.customerEngagement?.addToRecentlyViewed(productId);
};

window.updateBrowsingHistory = function(data) {
  return window.customerEngagement?.updateBrowsingHistory(data);
};

// Additional methods for component rendering
CustomerEngagementManager.prototype.loadProductsData = async function() {
  try {
    if (window.productsData) {
      return window.productsData;
    }

    const response = await fetch('/api/products.json');
    const products = await response.json();
    window.productsData = products;
    return products;
  } catch (error) {
    console.error('Failed to load products data:', error);
    return null;
  }
};

CustomerEngagementManager.prototype.getRecentlyViewedProductsFromData = function(allProducts, recentlyViewed) {
  const products = [];
  recentlyViewed.slice(0, 10).forEach(item => {
    const product = allProducts.find(p => p.id === item.productId);
    if (product) {
      products.push({ ...product, viewedAt: item.viewedAt, viewCount: item.viewCount });
    }
  });
  return products;
};

CustomerEngagementManager.prototype.renderRecentlyViewed = function(container, products, recentlyViewed) {
  container.innerHTML = '';

  products.forEach(product => {
    const viewData = recentlyViewed.find(item => item.productId === product.id);
    const card = this.createProductCard(product, {
      showViewCount: true,
      viewCount: viewData?.viewCount || 1,
      viewedAt: viewData?.viewedAt
    });
    container.appendChild(card);
  });
};

CustomerEngagementManager.prototype.renderRecommendations = function(container, products, type) {
  container.innerHTML = '';

  products.forEach(product => {
    const card = this.createProductCard(product, {
      showBadge: true,
      badgeText: this.getRecommendationBadge(type)
    });
    container.appendChild(card);
  });
};

CustomerEngagementManager.prototype.createProductCard = function(product, options = {}) {
  const card = document.createElement('div');
  card.className = 'product-card';
  card.setAttribute('data-product-id', product.id);

  const imageUrl = product.images?.[0] || '/images/product-placeholder.svg';
  const productUrl = `/products/${product.slug || product.id}/`;

  card.innerHTML = `
    <a href="${productUrl}" class="product-link">
      ${options.showBadge ? `<div class="recommendation-badge">${options.badgeText}</div>` : ''}
      <img src="${imageUrl}" alt="${product.name}" class="product-image" loading="lazy" />
      <div class="product-info">
        <h3 class="product-name">${product.name}</h3>
        <div class="product-price">$${product.price.toFixed(2)}</div>
        ${options.showViewCount ? `
          <div class="product-meta">
            <span class="view-count">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              ${options.viewCount} view${options.viewCount !== 1 ? 's' : ''}
            </span>
            <span class="viewed-time">${this.formatTimeAgo(options.viewedAt)}</span>
          </div>
        ` : ''}
      </div>
    </a>
  `;

  // Add click tracking
  card.addEventListener('click', () => {
    this.trackEvent('product_click', {
      productId: product.id,
      source: options.showBadge ? 'recommendations' : 'recently_viewed'
    });
  });

  return card;
};

CustomerEngagementManager.prototype.getRecommendationBadge = function(type) {
  switch (type) {
    case 'browsing': return 'For You';
    case 'similar': return 'Similar';
    case 'trending': return 'Trending';
    default: return 'Recommended';
  }
};

CustomerEngagementManager.prototype.formatTimeAgo = function(timestamp) {
  if (!timestamp) return '';

  const now = Date.now();
  const diff = now - timestamp;
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  return `${days}d ago`;
};

CustomerEngagementManager.prototype.updateRecommendationInsights = function(container, type) {
  if (!container) return;

  const history = this.getBrowsingHistory();
  const insights = this.generateInsights(history, type);

  if (insights.length === 0) {
    container.style.display = 'none';
    return;
  }

  container.style.display = 'block';
  const tagsContainer = container.querySelector(`#recommendations-${type}-insight-tags`);
  if (tagsContainer) {
    tagsContainer.innerHTML = '';
    insights.forEach(insight => {
      const tag = document.createElement('span');
      tag.className = 'insight-tag';
      tag.textContent = insight;
      tagsContainer.appendChild(tag);
    });
  }
};

CustomerEngagementManager.prototype.generateInsights = function(history, type) {
  const insights = [];

  if (type === 'browsing') {
    const topCategory = Object.keys(history.categories).reduce((a, b) =>
      history.categories[a] > history.categories[b] ? a : b, '');
    if (topCategory) insights.push(`You browse ${topCategory} frequently`);

    const topCondition = Object.keys(history.conditions).reduce((a, b) =>
      history.conditions[a] > history.conditions[b] ? a : b, '');
    if (topCondition) insights.push(`You prefer ${topCondition} condition`);
  }

  return insights.slice(0, 3);
};

CustomerEngagementManager.prototype.clearRecentlyViewed = function() {
  this.saveRecentlyViewed([]);
  this.updateRecentlyViewedDisplay();
  this.showToast('Recently viewed history cleared', 'info');
};

CustomerEngagementManager.prototype.refreshRecommendations = function(type) {
  this.updateRecommendationSection(type);
  this.showToast('Recommendations refreshed', 'success');
};
