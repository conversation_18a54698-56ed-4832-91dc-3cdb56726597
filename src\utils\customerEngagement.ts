/**
 * Customer Engagement Features for Cheers Marketplace
 * Handles wishlist, recently viewed, and personalization
 */

export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  condition: string;
  images: string[];
  slug?: string;
  createdAt?: string;
}

export interface WishlistItem {
  productId: string;
  addedAt: number;
  notifyOnSale?: boolean;
}

export interface RecentlyViewedItem {
  productId: string;
  viewedAt: number;
  viewCount: number;
}

export interface BrowsingHistory {
  categories: Record<string, number>;
  priceRanges: Record<string, number>;
  conditions: Record<string, number>;
  searchTerms: string[];
  lastActivity: number;
}

class CustomerEngagementManager {
  private readonly WISHLIST_KEY = 'cheers_wishlist';
  private readonly RECENTLY_VIEWED_KEY = 'cheers_recently_viewed';
  private readonly BROWSING_HISTORY_KEY = 'cheers_browsing_history';
  private readonly MAX_RECENTLY_VIEWED = 20;
  private readonly MAX_SEARCH_TERMS = 50;

  /**
   * Wishlist Management
   */
  
  addToWishlist(productId: string, notifyOnSale: boolean = false): boolean {
    try {
      const wishlist = this.getWishlist();
      
      // Check if already in wishlist
      if (wishlist.some(item => item.productId === productId)) {
        return false;
      }
      
      const newItem: WishlistItem = {
        productId,
        addedAt: Date.now(),
        notifyOnSale
      };
      
      wishlist.push(newItem);
      this.saveWishlist(wishlist);
      
      // Track analytics
      this.trackEvent('wishlist_add', { productId });
      
      return true;
    } catch (error) {
      console.error('Failed to add to wishlist:', error);
      return false;
    }
  }

  removeFromWishlist(productId: string): boolean {
    try {
      const wishlist = this.getWishlist();
      const filtered = wishlist.filter(item => item.productId !== productId);
      
      if (filtered.length === wishlist.length) {
        return false; // Item wasn't in wishlist
      }
      
      this.saveWishlist(filtered);
      
      // Track analytics
      this.trackEvent('wishlist_remove', { productId });
      
      return true;
    } catch (error) {
      console.error('Failed to remove from wishlist:', error);
      return false;
    }
  }

  isInWishlist(productId: string): boolean {
    const wishlist = this.getWishlist();
    return wishlist.some(item => item.productId === productId);
  }

  getWishlist(): WishlistItem[] {
    try {
      const stored = localStorage.getItem(this.WISHLIST_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load wishlist:', error);
      return [];
    }
  }

  getWishlistProducts(allProducts: Product[]): Product[] {
    const wishlist = this.getWishlist();
    const wishlistIds = new Set(wishlist.map(item => item.productId));
    
    return allProducts.filter(product => wishlistIds.has(product.id));
  }

  private saveWishlist(wishlist: WishlistItem[]): void {
    try {
      localStorage.setItem(this.WISHLIST_KEY, JSON.stringify(wishlist));
    } catch (error) {
      console.error('Failed to save wishlist:', error);
    }
  }

  /**
   * Recently Viewed Management
   */
  
  addToRecentlyViewed(productId: string): void {
    try {
      const recentlyViewed = this.getRecentlyViewed();
      
      // Remove if already exists
      const filtered = recentlyViewed.filter(item => item.productId !== productId);
      
      // Add to beginning
      const newItem: RecentlyViewedItem = {
        productId,
        viewedAt: Date.now(),
        viewCount: 1
      };
      
      // If item existed, increment view count
      const existing = recentlyViewed.find(item => item.productId === productId);
      if (existing) {
        newItem.viewCount = existing.viewCount + 1;
      }
      
      filtered.unshift(newItem);
      
      // Limit size
      const trimmed = filtered.slice(0, this.MAX_RECENTLY_VIEWED);
      
      this.saveRecentlyViewed(trimmed);
      
      // Track analytics
      this.trackEvent('product_view', { productId });
      
    } catch (error) {
      console.error('Failed to add to recently viewed:', error);
    }
  }

  getRecentlyViewed(): RecentlyViewedItem[] {
    try {
      const stored = localStorage.getItem(this.RECENTLY_VIEWED_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load recently viewed:', error);
      return [];
    }
  }

  getRecentlyViewedProducts(allProducts: Product[], limit: number = 10): Product[] {
    const recentlyViewed = this.getRecentlyViewed();
    const recentIds = recentlyViewed.slice(0, limit).map(item => item.productId);
    
    // Maintain order from recently viewed
    const products: Product[] = [];
    recentIds.forEach(id => {
      const product = allProducts.find(p => p.id === id);
      if (product) {
        products.push(product);
      }
    });
    
    return products;
  }

  private saveRecentlyViewed(recentlyViewed: RecentlyViewedItem[]): void {
    try {
      localStorage.setItem(this.RECENTLY_VIEWED_KEY, JSON.stringify(recentlyViewed));
    } catch (error) {
      console.error('Failed to save recently viewed:', error);
    }
  }

  /**
   * Browsing History & Personalization
   */
  
  updateBrowsingHistory(data: {
    category?: string;
    priceRange?: string;
    condition?: string;
    searchTerm?: string;
  }): void {
    try {
      const history = this.getBrowsingHistory();
      
      // Update categories
      if (data.category) {
        history.categories[data.category] = (history.categories[data.category] || 0) + 1;
      }
      
      // Update price ranges
      if (data.priceRange) {
        history.priceRanges[data.priceRange] = (history.priceRanges[data.priceRange] || 0) + 1;
      }
      
      // Update conditions
      if (data.condition) {
        history.conditions[data.condition] = (history.conditions[data.condition] || 0) + 1;
      }
      
      // Update search terms
      if (data.searchTerm && data.searchTerm.trim()) {
        const term = data.searchTerm.trim().toLowerCase();
        if (!history.searchTerms.includes(term)) {
          history.searchTerms.unshift(term);
          history.searchTerms = history.searchTerms.slice(0, this.MAX_SEARCH_TERMS);
        }
      }
      
      history.lastActivity = Date.now();
      
      this.saveBrowsingHistory(history);
      
    } catch (error) {
      console.error('Failed to update browsing history:', error);
    }
  }

  getBrowsingHistory(): BrowsingHistory {
    try {
      const stored = localStorage.getItem(this.BROWSING_HISTORY_KEY);
      return stored ? JSON.parse(stored) : {
        categories: {},
        priceRanges: {},
        conditions: {},
        searchTerms: [],
        lastActivity: Date.now()
      };
    } catch (error) {
      console.error('Failed to load browsing history:', error);
      return {
        categories: {},
        priceRanges: {},
        conditions: {},
        searchTerms: [],
        lastActivity: Date.now()
      };
    }
  }

  getPersonalizedRecommendations(allProducts: Product[], limit: number = 8): Product[] {
    const history = this.getBrowsingHistory();
    const recentlyViewed = this.getRecentlyViewed();
    const wishlist = this.getWishlist();
    
    // Get excluded product IDs
    const excludedIds = new Set([
      ...recentlyViewed.map(item => item.productId),
      ...wishlist.map(item => item.productId)
    ]);
    
    // Score products based on browsing history
    const scoredProducts = allProducts
      .filter(product => !excludedIds.has(product.id))
      .map(product => {
        let score = 0;
        
        // Category preference
        const categoryScore = history.categories[product.category] || 0;
        score += categoryScore * 0.4;
        
        // Condition preference
        const conditionScore = history.conditions[product.condition] || 0;
        score += conditionScore * 0.2;
        
        // Price range preference
        const priceRange = this.getPriceRange(product.price);
        const priceScore = history.priceRanges[priceRange] || 0;
        score += priceScore * 0.2;
        
        // Search term relevance
        const searchScore = this.calculateSearchRelevance(product, history.searchTerms);
        score += searchScore * 0.2;
        
        return { product, score };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.product);
    
    return scoredProducts;
  }

  private getPriceRange(price: number): string {
    if (price < 10) return '0-10';
    if (price < 25) return '10-25';
    if (price < 50) return '25-50';
    if (price < 100) return '50-100';
    return '100+';
  }

  private calculateSearchRelevance(product: Product, searchTerms: string[]): number {
    let relevance = 0;
    const productText = `${product.name} ${product.category}`.toLowerCase();
    
    searchTerms.forEach(term => {
      if (productText.includes(term)) {
        relevance += 1;
      }
    });
    
    return relevance;
  }

  private saveBrowsingHistory(history: BrowsingHistory): void {
    try {
      localStorage.setItem(this.BROWSING_HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Failed to save browsing history:', error);
    }
  }

  /**
   * Sale Notifications
   */
  
  checkForSaleNotifications(allProducts: Product[]): Product[] {
    const wishlist = this.getWishlist();
    const saleProducts: Product[] = [];
    
    wishlist.forEach(item => {
      if (item.notifyOnSale) {
        const product = allProducts.find(p => p.id === item.productId);
        if (product) {
          // Check if product is on sale (this would need to be implemented based on your sale logic)
          // For now, we'll just return products that have been in wishlist for a while
          const daysSinceAdded = (Date.now() - item.addedAt) / (1000 * 60 * 60 * 24);
          if (daysSinceAdded > 7) {
            saleProducts.push(product);
          }
        }
      }
    });
    
    return saleProducts;
  }

  /**
   * Analytics
   */
  
  private trackEvent(eventName: string, data: any): void {
    // Integration with your analytics system
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'Customer Engagement',
        ...data
      });
    }
  }

  /**
   * Data Export/Import
   */
  
  exportData(): {
    wishlist: WishlistItem[];
    recentlyViewed: RecentlyViewedItem[];
    browsingHistory: BrowsingHistory;
  } {
    return {
      wishlist: this.getWishlist(),
      recentlyViewed: this.getRecentlyViewed(),
      browsingHistory: this.getBrowsingHistory()
    };
  }

  importData(data: {
    wishlist?: WishlistItem[];
    recentlyViewed?: RecentlyViewedItem[];
    browsingHistory?: BrowsingHistory;
  }): void {
    try {
      if (data.wishlist) {
        this.saveWishlist(data.wishlist);
      }
      if (data.recentlyViewed) {
        this.saveRecentlyViewed(data.recentlyViewed);
      }
      if (data.browsingHistory) {
        this.saveBrowsingHistory(data.browsingHistory);
      }
    } catch (error) {
      console.error('Failed to import data:', error);
    }
  }

  /**
   * Clear all data
   */
  
  clearAllData(): void {
    try {
      localStorage.removeItem(this.WISHLIST_KEY);
      localStorage.removeItem(this.RECENTLY_VIEWED_KEY);
      localStorage.removeItem(this.BROWSING_HISTORY_KEY);
    } catch (error) {
      console.error('Failed to clear data:', error);
    }
  }
}

// Singleton instance
export const customerEngagement = new CustomerEngagementManager();

// Convenience functions
export const addToWishlist = (productId: string, notifyOnSale?: boolean) => 
  customerEngagement.addToWishlist(productId, notifyOnSale);

export const removeFromWishlist = (productId: string) => 
  customerEngagement.removeFromWishlist(productId);

export const isInWishlist = (productId: string) => 
  customerEngagement.isInWishlist(productId);

export const addToRecentlyViewed = (productId: string) => 
  customerEngagement.addToRecentlyViewed(productId);

export const getRecentlyViewedProducts = (allProducts: Product[], limit?: number) => 
  customerEngagement.getRecentlyViewedProducts(allProducts, limit);

export const getPersonalizedRecommendations = (allProducts: Product[], limit?: number) => 
  customerEngagement.getPersonalizedRecommendations(allProducts, limit);

export const updateBrowsingHistory = (data: any) => 
  customerEngagement.updateBrowsingHistory(data);

export const getWishlistProducts = (allProducts: Product[]) => 
  customerEngagement.getWishlistProducts(allProducts);
