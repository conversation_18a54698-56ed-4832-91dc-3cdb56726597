---
// Personalized Recommendations Component
export interface Props {
  title?: string;
  subtitle?: string;
  limit?: number;
  showTitle?: boolean;
  className?: string;
  layout?: 'horizontal' | 'grid';
  recommendationType?: 'browsing' | 'similar' | 'trending';
}

const {
  title = "Recommended for You",
  subtitle = "Based on your browsing history",
  limit = 8,
  showTitle = true,
  className = "",
  layout = "horizontal",
  recommendationType = "browsing"
} = Astro.props;

const sectionId = `recommendations-${recommendationType}`;
---

<section class={`recommendations-section ${className}`} id={sectionId} style="display: none;">
  {showTitle && (
    <div class="section-header">
      <div class="header-content">
        <h2 class="section-title">{title}</h2>
        {subtitle && <p class="section-subtitle">{subtitle}</p>}
      </div>
      <div class="header-actions">
        <button class="refresh-btn" id={`refresh-${recommendationType}`} type="button" aria-label="Refresh recommendations">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="23,4 23,10 17,10"></polyline>
            <polyline points="1,20 1,14 7,14"></polyline>
            <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"></path>
          </svg>
          Refresh
        </button>
      </div>
    </div>
  )}

  <div class={`recommendations-container ${layout}`}>
    <div class="recommendations-grid" id={`${sectionId}-grid`}>
      <!-- Products will be populated by JavaScript -->
    </div>
    
    <!-- Navigation arrows for horizontal layout -->
    {layout === 'horizontal' && (
      <div class="navigation-controls">
        <button class="nav-btn prev-btn" id={`${sectionId}-prev`} aria-label="Previous recommendations">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
        </button>
        <button class="nav-btn next-btn" id={`${sectionId}-next`} aria-label="Next recommendations">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </button>
      </div>
    )}
  </div>

  <!-- Loading state -->
  <div class="loading-state" id={`${sectionId}-loading`} style="display: none;">
    <div class="loading-grid">
      {Array.from({ length: 4 }, (_, i) => (
        <div class="loading-card" key={i}>
          <div class="loading-image"></div>
          <div class="loading-content">
            <div class="loading-line loading-title"></div>
            <div class="loading-line loading-price"></div>
          </div>
        </div>
      ))}
    </div>
  </div>

  <!-- Empty state -->
  <div class="empty-state" id={`${sectionId}-empty`} style="display: none;">
    <div class="empty-content">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
        <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
      </svg>
      <h3>No recommendations yet</h3>
      <p>Browse some products to get personalized recommendations.</p>
      <a href="/products/" class="browse-btn">Start Shopping</a>
    </div>
  </div>

  <!-- Recommendation insights -->
  <div class="recommendation-insights" id={`${sectionId}-insights`} style="display: none;">
    <div class="insights-content">
      <h4>Why these recommendations?</h4>
      <div class="insight-tags" id={`${sectionId}-insight-tags`}>
        <!-- Insight tags will be populated by JavaScript -->
      </div>
    </div>
  </div>
</section>

<style>
  .recommendations-section {
    margin: 2rem 0;
    background: #fafafa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
  }

  .header-content {
    flex: 1;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
  }

  .section-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;
  }

  .refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .refresh-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
  }

  .refresh-btn:active {
    transform: scale(0.98);
  }

  .recommendations-container {
    position: relative;
  }

  .recommendations-container.horizontal {
    overflow: hidden;
  }

  .recommendations-grid {
    display: grid;
    gap: 1rem;
    transition: transform 0.3s ease;
  }

  .recommendations-container.horizontal .recommendations-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-auto-flow: column;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .recommendations-container.horizontal .recommendations-grid::-webkit-scrollbar {
    display: none;
  }

  .recommendations-container.grid .recommendations-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .product-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
  }

  .product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }

  .product-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: #f3f4f6;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-price {
    font-weight: 600;
    color: #059669;
    font-size: 1rem;
  }

  .recommendation-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: #2563eb;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .navigation-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 2;
  }

  .nav-btn {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    pointer-events: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .nav-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: scale(1.05);
  }

  .nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .nav-btn.prev-btn {
    left: -20px;
  }

  .nav-btn.next-btn {
    right: -20px;
  }

  /* Loading state */
  .loading-state {
    margin: 1rem 0;
  }

  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .loading-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
  }

  .loading-image {
    width: 100%;
    height: 150px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  .loading-content {
    padding: 1rem;
  }

  .loading-line {
    height: 1rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .loading-title {
    width: 80%;
  }

  .loading-price {
    width: 40%;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* Empty state */
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
  }

  .empty-content svg {
    margin-bottom: 1rem;
    color: #9ca3af;
  }

  .empty-content h3 {
    font-size: 1.125rem;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .empty-content p {
    margin-bottom: 1.5rem;
  }

  .browse-btn {
    display: inline-flex;
    align-items: center;
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .browse-btn:hover {
    background: #1d4ed8;
  }

  /* Recommendation insights */
  .recommendation-insights {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  .recommendation-insights h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin: 0 0 0.5rem 0;
  }

  .insight-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .insight-tag {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .recommendations-container.horizontal .recommendations-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }

    .recommendations-container.grid .recommendations-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .loading-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }

    .product-image,
    .loading-image {
      height: 120px;
    }

    .product-info,
    .loading-content {
      padding: 0.75rem;
    }

    .nav-btn {
      width: 36px;
      height: 36px;
    }

    .nav-btn.prev-btn {
      left: -18px;
    }

    .nav-btn.next-btn {
      right: -18px;
    }
  }

  @media (max-width: 480px) {
    .recommendations-section {
      padding: 1rem;
    }

    .recommendations-container.horizontal .recommendations-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .recommendations-container.grid .recommendations-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .loading-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .product-card,
    .loading-card {
      border-width: 2px;
    }

    .nav-btn {
      border-width: 2px;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .product-card,
    .nav-btn,
    .recommendations-grid {
      transition: none;
    }

    .product-card:hover {
      transform: none;
    }

    .nav-btn:hover {
      transform: none;
    }

    .loading-image,
    .loading-line {
      animation: none;
    }
  }
</style>

<script>
  // Personalized recommendations functionality will be handled by the customer engagement script
  // This component provides the structure and styling
</script>
