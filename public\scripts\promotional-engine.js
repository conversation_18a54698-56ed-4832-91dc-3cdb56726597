/**
 * Promotional Engine JavaScript for Browser
 * Handles promotions, discounts, and promotional campaigns
 */

class PromotionalEngine {
  constructor() {
    this.promotions = [];
    this.STORAGE_KEY = 'cheers_promotions';
    this.currentFilter = 'all';
    this.currentTypeFilter = 'all';
    this.currentSearch = '';
    this.editingPromotion = null;
    
    this.init();
  }

  init() {
    this.loadPromotions();
    this.initializeSeasonalPromotions();
    this.bindEvents();
    this.updateDisplay();
  }

  bindEvents() {
    // Add promotion button
    document.getElementById('add-promotion-btn')?.addEventListener('click', () => {
      this.showPromotionForm();
    });

    // Refresh button
    document.getElementById('refresh-promotions')?.addEventListener('click', () => {
      this.refreshPromotions();
    });

    // Filters
    document.getElementById('promotion-status-filter')?.addEventListener('change', (e) => {
      this.currentFilter = e.target.value;
      this.updatePromotionsList();
    });

    document.getElementById('promotion-type-filter')?.addEventListener('change', (e) => {
      this.currentTypeFilter = e.target.value;
      this.updatePromotionsList();
    });

    document.getElementById('promotion-search')?.addEventListener('input', (e) => {
      this.currentSearch = e.target.value.toLowerCase();
      this.updatePromotionsList();
    });

    // Modal events
    document.getElementById('close-promotion-modal')?.addEventListener('click', () => {
      this.hidePromotionForm();
    });

    document.getElementById('cancel-promotion')?.addEventListener('click', () => {
      this.hidePromotionForm();
    });

    // Form events
    document.getElementById('promotion-form')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.savePromotion();
    });

    document.getElementById('promo-type')?.addEventListener('change', (e) => {
      this.updateFormHelp(e.target.value);
    });

    // Click outside modal to close
    document.getElementById('promotion-modal')?.addEventListener('click', (e) => {
      if (e.target.id === 'promotion-modal') {
        this.hidePromotionForm();
      }
    });
  }

  loadPromotions() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.promotions = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load promotions:', error);
      this.promotions = [];
    }
  }

  savePromotions() {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.promotions));
    } catch (error) {
      console.error('Failed to save promotions:', error);
    }
  }

  initializeSeasonalPromotions() {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    const seasonalPromotions = [
      {
        name: 'New Year Sale',
        description: '20% off everything to start the year right!',
        type: 'percentage',
        value: 20,
        conditions: {},
        startDate: `${currentYear}-01-01`,
        endDate: `${currentYear}-01-07`,
        isActive: true,
        priority: 10,
        usageCount: 0
      },
      {
        name: 'Spring Cleaning Sale',
        description: 'Buy 2 get 1 free on home items',
        type: 'bogo',
        value: 1,
        conditions: { categories: ['Home Decor', 'Furniture'] },
        startDate: `${currentYear}-03-15`,
        endDate: `${currentYear}-04-15`,
        isActive: true,
        priority: 7,
        usageCount: 0
      },
      {
        name: 'Summer Clearance',
        description: '30% off summer items',
        type: 'percentage',
        value: 30,
        conditions: { tags: ['summer', 'outdoor'] },
        startDate: `${currentYear}-07-01`,
        endDate: `${currentYear}-08-31`,
        isActive: true,
        priority: 9,
        usageCount: 0
      },
      {
        name: 'Black Friday',
        description: '40% off everything!',
        type: 'percentage',
        value: 40,
        conditions: {},
        startDate: `${currentYear}-11-24`,
        endDate: `${currentYear}-11-24`,
        isActive: true,
        priority: 15,
        usageCount: 0
      }
    ];

    // Add seasonal promotions if they don't exist
    seasonalPromotions.forEach(promo => {
      if (!this.promotions.some(p => p.name === promo.name)) {
        this.createPromotion(promo);
      }
    });
  }

  createPromotion(promotionData) {
    const promotion = {
      ...promotionData,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.promotions.push(promotion);
    this.savePromotions();
    return promotion;
  }

  updatePromotion(id, updates) {
    const index = this.promotions.findIndex(p => p.id === id);
    if (index === -1) return null;

    this.promotions[index] = {
      ...this.promotions[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.savePromotions();
    return this.promotions[index];
  }

  deletePromotion(id) {
    const index = this.promotions.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.promotions.splice(index, 1);
    this.savePromotions();
    return true;
  }

  getPromotionStats() {
    const now = new Date();
    let active = 0;
    let expired = 0;
    let upcoming = 0;

    this.promotions.forEach(promo => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);

      if (now < startDate) {
        upcoming++;
      } else if (now > endDate) {
        expired++;
      } else if (promo.isActive) {
        active++;
      }
    });

    return {
      total: this.promotions.length,
      active,
      expired,
      upcoming
    };
  }

  updateDisplay() {
    this.updateStats();
    this.updatePromotionsList();
    this.populateCategories();
  }

  updateStats() {
    const stats = this.getPromotionStats();
    
    document.getElementById('active-promotions').textContent = stats.active;
    document.getElementById('upcoming-promotions').textContent = stats.upcoming;
    document.getElementById('expired-promotions').textContent = stats.expired;
    document.getElementById('total-promotions').textContent = stats.total;
  }

  updatePromotionsList() {
    const container = document.getElementById('promotions-list');
    if (!container) return;

    const filteredPromotions = this.getFilteredPromotions();
    
    if (filteredPromotions.length === 0) {
      container.innerHTML = `
        <div class="empty-state" style="padding: 3rem; text-align: center; color: #6b7280;">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" style="margin-bottom: 1rem;">
            <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
          </svg>
          <h3>No promotions found</h3>
          <p>Create your first promotion to get started.</p>
        </div>
      `;
      return;
    }

    container.innerHTML = '';
    
    filteredPromotions.forEach(promotion => {
      const item = this.createPromotionItem(promotion);
      container.appendChild(item);
    });
  }

  getFilteredPromotions() {
    return this.promotions.filter(promo => {
      // Status filter
      if (this.currentFilter !== 'all') {
        const status = this.getPromotionStatus(promo);
        if (status !== this.currentFilter) return false;
      }

      // Type filter
      if (this.currentTypeFilter !== 'all') {
        if (promo.type !== this.currentTypeFilter) return false;
      }

      // Search filter
      if (this.currentSearch) {
        const searchText = `${promo.name} ${promo.description}`.toLowerCase();
        if (!searchText.includes(this.currentSearch)) return false;
      }

      return true;
    });
  }

  getPromotionStatus(promotion) {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);

    if (now < startDate) return 'upcoming';
    if (now > endDate) return 'expired';
    if (promotion.isActive) return 'active';
    return 'inactive';
  }

  createPromotionItem(promotion) {
    const item = document.createElement('div');
    item.className = 'promotion-item';
    
    const status = this.getPromotionStatus(promotion);
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);
    
    const valueText = promotion.type === 'percentage' 
      ? `${promotion.value}%` 
      : promotion.type === 'fixed' 
        ? `$${promotion.value}` 
        : promotion.type === 'bogo'
          ? `Buy ${promotion.value + 1} Get ${promotion.value}`
          : promotion.value;

    item.innerHTML = `
      <div class="promotion-info">
        <div class="promotion-name">${promotion.name}</div>
        <div class="promotion-description">${promotion.description}</div>
        <div class="promotion-meta">
          <span>Type: ${promotion.type}</span>
          <span>Value: ${valueText}</span>
          <span>Priority: ${promotion.priority}</span>
          <span>${promotion.startDate} - ${promotion.endDate}</span>
        </div>
      </div>
      <div class="promotion-status ${status}">${statusText}</div>
      <div class="promotion-actions">
        <button class="btn-icon" onclick="promotionalEngine.editPromotion('${promotion.id}')" title="Edit">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </button>
        <button class="btn-icon" onclick="promotionalEngine.togglePromotion('${promotion.id}')" title="${promotion.isActive ? 'Deactivate' : 'Activate'}">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            ${promotion.isActive 
              ? '<path d="M10 9v6l5-3z"></path><circle cx="12" cy="12" r="10"></circle>'
              : '<rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect>'
            }
          </svg>
        </button>
        <button class="btn-icon" onclick="promotionalEngine.deletePromotionConfirm('${promotion.id}')" title="Delete">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="3,6 5,6 21,6"></polyline>
            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"></path>
          </svg>
        </button>
      </div>
    `;

    return item;
  }

  showPromotionForm(promotion = null) {
    this.editingPromotion = promotion;
    const modal = document.getElementById('promotion-modal');
    const title = document.getElementById('promotion-form-title');
    
    if (promotion) {
      title.textContent = 'Edit Promotion';
      this.populateForm(promotion);
    } else {
      title.textContent = 'Create Promotion';
      this.resetForm();
    }
    
    modal.style.display = 'flex';
  }

  hidePromotionForm() {
    document.getElementById('promotion-modal').style.display = 'none';
    this.editingPromotion = null;
    this.resetForm();
  }

  populateForm(promotion) {
    document.getElementById('promo-name').value = promotion.name || '';
    document.getElementById('promo-description').value = promotion.description || '';
    document.getElementById('promo-type').value = promotion.type || '';
    document.getElementById('promo-value').value = promotion.value || '';
    document.getElementById('promo-priority').value = promotion.priority || 5;
    document.getElementById('promo-start-date').value = promotion.startDate || '';
    document.getElementById('promo-end-date').value = promotion.endDate || '';
    document.getElementById('promo-active').checked = promotion.isActive !== false;
    document.getElementById('promo-min-order').value = promotion.conditions?.minOrderValue || '';
    document.getElementById('promo-tags').value = promotion.conditions?.tags?.join(', ') || '';
    document.getElementById('promo-usage-limit').value = promotion.usageLimit || '';

    // Set categories and conditions
    this.setMultiSelectValues('promo-categories', promotion.conditions?.categories || []);
    this.setMultiSelectValues('promo-conditions', promotion.conditions?.conditions || []);
    
    this.updateFormHelp(promotion.type);
  }

  resetForm() {
    document.getElementById('promotion-form').reset();
    document.getElementById('promo-priority').value = 5;
    document.getElementById('promo-active').checked = true;
    
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    document.getElementById('promo-start-date').value = today;
    document.getElementById('promo-end-date').value = nextWeek;
  }

  savePromotion() {
    const formData = this.getFormData();
    
    if (!this.validateForm(formData)) {
      return;
    }

    if (this.editingPromotion) {
      this.updatePromotion(this.editingPromotion.id, formData);
    } else {
      this.createPromotion(formData);
    }

    this.hidePromotionForm();
    this.updateDisplay();
    this.showToast('Promotion saved successfully!', 'success');
  }

  getFormData() {
    const categories = this.getMultiSelectValues('promo-categories');
    const conditions = this.getMultiSelectValues('promo-conditions');
    const tags = document.getElementById('promo-tags').value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    return {
      name: document.getElementById('promo-name').value,
      description: document.getElementById('promo-description').value,
      type: document.getElementById('promo-type').value,
      value: parseFloat(document.getElementById('promo-value').value),
      priority: parseInt(document.getElementById('promo-priority').value),
      startDate: document.getElementById('promo-start-date').value,
      endDate: document.getElementById('promo-end-date').value,
      isActive: document.getElementById('promo-active').checked,
      usageLimit: parseInt(document.getElementById('promo-usage-limit').value) || undefined,
      usageCount: this.editingPromotion?.usageCount || 0,
      conditions: {
        minOrderValue: parseFloat(document.getElementById('promo-min-order').value) || undefined,
        categories: categories.length > 0 ? categories : undefined,
        conditions: conditions.length > 0 ? conditions : undefined,
        tags: tags.length > 0 ? tags : undefined
      }
    };
  }

  validateForm(data) {
    if (!data.name.trim()) {
      this.showToast('Please enter a promotion name', 'error');
      return false;
    }

    if (!data.type) {
      this.showToast('Please select a promotion type', 'error');
      return false;
    }

    if (!data.value || data.value <= 0) {
      this.showToast('Please enter a valid value', 'error');
      return false;
    }

    if (data.type === 'percentage' && data.value > 100) {
      this.showToast('Percentage discount cannot exceed 100%', 'error');
      return false;
    }

    if (!data.startDate || !data.endDate) {
      this.showToast('Please set start and end dates', 'error');
      return false;
    }

    if (new Date(data.startDate) >= new Date(data.endDate)) {
      this.showToast('End date must be after start date', 'error');
      return false;
    }

    return true;
  }

  editPromotion(id) {
    const promotion = this.promotions.find(p => p.id === id);
    if (promotion) {
      this.showPromotionForm(promotion);
    }
  }

  togglePromotion(id) {
    const promotion = this.promotions.find(p => p.id === id);
    if (promotion) {
      this.updatePromotion(id, { isActive: !promotion.isActive });
      this.updateDisplay();
      this.showToast(`Promotion ${promotion.isActive ? 'deactivated' : 'activated'}`, 'info');
    }
  }

  deletePromotionConfirm(id) {
    const promotion = this.promotions.find(p => p.id === id);
    if (promotion && confirm(`Are you sure you want to delete "${promotion.name}"?`)) {
      this.deletePromotion(id);
      this.updateDisplay();
      this.showToast('Promotion deleted', 'info');
    }
  }

  refreshPromotions() {
    this.updateDisplay();
    this.showToast('Promotions refreshed', 'success');
  }

  populateCategories() {
    const select = document.getElementById('promo-categories');
    if (!select) return;

    // Get categories from products if available
    const categories = window.adminPanel?.categories || [
      'Clothing', 'Books', 'Home Decor', 'Electronics', 'Toys & Games', 'Arts & Crafts'
    ];

    select.innerHTML = '';
    categories.forEach(category => {
      const option = document.createElement('option');
      option.value = category;
      option.textContent = category;
      select.appendChild(option);
    });
  }

  updateFormHelp(type) {
    const helpElement = document.getElementById('promo-value-help');
    if (!helpElement) return;

    switch (type) {
      case 'percentage':
        helpElement.textContent = 'Enter percentage (0-100)';
        break;
      case 'fixed':
        helpElement.textContent = 'Enter dollar amount';
        break;
      case 'bogo':
        helpElement.textContent = 'Enter number of free items (e.g., 1 for "buy 2 get 1")';
        break;
      default:
        helpElement.textContent = 'Enter value based on promotion type';
    }
  }

  setMultiSelectValues(selectId, values) {
    const select = document.getElementById(selectId);
    if (!select) return;

    Array.from(select.options).forEach(option => {
      option.selected = values.includes(option.value);
    });
  }

  getMultiSelectValues(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return [];

    return Array.from(select.selectedOptions).map(option => option.value);
  }

  generateId() {
    return `promo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  showToast(message, type = 'info') {
    if (window.showToast) {
      window.showToast(message, type);
    } else {
      console.log(`Toast: ${message} (${type})`);
    }
  }

  // Public API for cart integration
  applyPromotionsToProduct(product) {
    // This would integrate with the TypeScript promotional engine
    // For now, return empty array
    return [];
  }

  applyPromotionsToCart(cartItems) {
    // This would integrate with the TypeScript promotional engine
    // For now, return the cart as-is
    return {
      items: cartItems,
      subtotal: cartItems.reduce((sum, item) => sum + (item.originalPrice * item.quantity), 0),
      totalDiscount: 0,
      finalTotal: cartItems.reduce((sum, item) => sum + (item.originalPrice * item.quantity), 0),
      appliedPromotions: []
    };
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('section-promotions')) {
    window.promotionalEngine = new PromotionalEngine();
  }
});
